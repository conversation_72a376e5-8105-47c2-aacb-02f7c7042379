@page "/settings"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<PageTitle>الإعدادات</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cog me-2"></i>إعدادات النظام</h2>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="list-group">
            <button class="list-group-item list-group-item-action @(activeTab == "departments" ? "active" : "")" 
                    @onclick="() => SetActiveTab(\"departments\")">
                <i class="fas fa-building me-2"></i>إدارة الإدارات
            </button>
            <button class="list-group-item list-group-item-action @(activeTab == "positions" ? "active" : "")" 
                    @onclick="() => SetActiveTab(\"positions\")">
                <i class="fas fa-briefcase me-2"></i>إدارة المناصب
            </button>
            <button class="list-group-item list-group-item-action @(activeTab == "users" ? "active" : "")" 
                    @onclick="() => SetActiveTab(\"users\")">
                <i class="fas fa-users-cog me-2"></i>إدارة المستخدمين
            </button>
            <button class="list-group-item list-group-item-action @(activeTab == "system" ? "active" : "")" 
                    @onclick="() => SetActiveTab(\"system\")">
                <i class="fas fa-server me-2"></i>إعدادات النظام
            </button>
        </div>
    </div>
    
    <div class="col-md-9">
        @if (activeTab == "departments")
        {
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>إدارة الإدارات</h5>
                    <button class="btn btn-success btn-sm" @onclick="ShowAddDepartmentModal">
                        <i class="fas fa-plus me-1"></i>إضافة إدارة
                    </button>
                </div>
                <div class="card-body">
                    @if (departments == null)
                    {
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    }
                    else if (!departments.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد إدارات</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الإدارة</th>
                                        <th>الوصف</th>
                                        <th>عدد الموظفين</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var dept in departments)
                                    {
                                        <tr>
                                            <td class="fw-bold">@dept.Name</td>
                                            <td>@dept.Description</td>
                                            <td>
                                                <span class="badge bg-primary">@dept.Employees.Count()</span>
                                            </td>
                                            <td>@dept.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-warning" @onclick="() => EditDepartment(dept)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => ConfirmDeleteDepartment(dept.Id, dept.Name)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        }
        else if (activeTab == "positions")
        {
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>إدارة المناصب</h5>
                    <button class="btn btn-success btn-sm" @onclick="ShowAddPositionModal">
                        <i class="fas fa-plus me-1"></i>إضافة منصب
                    </button>
                </div>
                <div class="card-body">
                    @if (positions == null)
                    {
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    }
                    else if (!positions.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-briefcase fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد مناصب</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المنصب</th>
                                        <th>الوصف</th>
                                        <th>عدد الموظفين</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var position in positions)
                                    {
                                        <tr>
                                            <td class="fw-bold">@position.Name</td>
                                            <td>@position.Description</td>
                                            <td>
                                                <span class="badge bg-primary">@position.Employees.Count()</span>
                                            </td>
                                            <td>@position.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-warning" @onclick="() => EditPosition(position)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => ConfirmDeletePosition(position.Id, position.Name)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        }
        else if (activeTab == "users")
        {
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومة:</strong> إدارة المستخدمين والأدوار ستكون متاحة في الإصدار القادم.
                        <br />
                        حالياً يمكن إنشاء مستخدمين جدد من خلال صفحة التسجيل.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                                    <h6>المديرين</h6>
                                    <p class="text-muted">صلاحيات كاملة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-user fa-2x text-success mb-2"></i>
                                    <h6>المستخدمين</h6>
                                    <p class="text-muted">صلاحيات محدودة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (activeTab == "system")
        {
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>إعدادات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم النظام</label>
                            <input type="text" class="form-control" value="نظام التدريب" readonly />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">إصدار النظام</label>
                            <input type="text" class="form-control" value="1.0.0" readonly />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ آخر تحديث</label>
                            <input type="text" class="form-control" value="@DateTime.Now.ToString("dd/MM/yyyy")" readonly />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة النظام</label>
                            <input type="text" class="form-control text-success" value="نشط" readonly />
                        </div>
                    </div>
                    
                    <hr />
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h6>إحصائيات النظام</h6>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-primary">@systemStats.TotalEmployees</h4>
                                        <small>الموظفين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-success">@systemStats.TotalCourses</h4>
                                        <small>الدورات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-warning">@systemStats.TotalNominations</h4>
                                        <small>الترشيحات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-info">@systemStats.TotalCertificates</h4>
                                        <small>الشهادات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Department Modal -->
<div class="modal fade @(showDepartmentModal ? "show d-block" : "")" tabindex="-1" style="@(showDepartmentModal ? "background-color: rgba(0,0,0,0.5);" : "")">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@(editingDepartment?.Id > 0 ? "تعديل الإدارة" : "إضافة إدارة جديدة")</h5>
                <button type="button" class="btn-close" @onclick="CloseDepartmentModal"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="editingDepartment" OnValidSubmit="SaveDepartment">
                    <DataAnnotationsValidator />
                    <div class="mb-3">
                        <label class="form-label">اسم الإدارة <span class="text-danger">*</span></label>
                        <InputText @bind-Value="editingDepartment.Name" class="form-control" />
                        <ValidationMessage For="@(() => editingDepartment.Name)" class="text-danger" />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <InputTextArea @bind-Value="editingDepartment.Description" class="form-control" rows="3" />
                        <ValidationMessage For="@(() => editingDepartment.Description)" class="text-danger" />
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" @onclick="CloseDepartmentModal">إلغاء</button>
                        <button type="submit" class="btn btn-success">حفظ</button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

<!-- Position Modal -->
<div class="modal fade @(showPositionModal ? "show d-block" : "")" tabindex="-1" style="@(showPositionModal ? "background-color: rgba(0,0,0,0.5);" : "")">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@(editingPosition?.Id > 0 ? "تعديل المنصب" : "إضافة منصب جديد")</h5>
                <button type="button" class="btn-close" @onclick="ClosePositionModal"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="editingPosition" OnValidSubmit="SavePosition">
                    <DataAnnotationsValidator />
                    <div class="mb-3">
                        <label class="form-label">اسم المنصب <span class="text-danger">*</span></label>
                        <InputText @bind-Value="editingPosition.Name" class="form-control" />
                        <ValidationMessage For="@(() => editingPosition.Name)" class="text-danger" />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <InputTextArea @bind-Value="editingPosition.Description" class="form-control" rows="3" />
                        <ValidationMessage For="@(() => editingPosition.Description)" class="text-danger" />
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" @onclick="ClosePositionModal">إلغاء</button>
                        <button type="submit" class="btn btn-success">حفظ</button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private string activeTab = "departments";
    private List<Department>? departments;
    private List<Position>? positions;
    
    // Modals
    private bool showDepartmentModal = false;
    private bool showPositionModal = false;
    private Department editingDepartment = new();
    private Position editingPosition = new();
    
    // System stats
    private SystemStats systemStats = new();
    
    public class SystemStats
    {
        public int TotalEmployees { get; set; }
        public int TotalCourses { get; set; }
        public int TotalNominations { get; set; }
        public int TotalCertificates { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        await LoadSystemStats();
    }

    private async Task LoadData()
    {
        await LoadDepartments();
        await LoadPositions();
    }

    private async Task LoadDepartments()
    {
        departments = await DbContext.Departments
            .Include(d => d.Employees)
            .OrderBy(d => d.Name)
            .ToListAsync();
    }

    private async Task LoadPositions()
    {
        positions = await DbContext.Positions
            .Include(p => p.Employees)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    private async Task LoadSystemStats()
    {
        systemStats.TotalEmployees = await DbContext.Employees.CountAsync();
        systemStats.TotalCourses = await DbContext.Courses.CountAsync();
        systemStats.TotalNominations = await DbContext.CourseNominations.CountAsync();
        systemStats.TotalCertificates = await DbContext.Certificates.CountAsync();
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
    }

    // Department methods
    private void ShowAddDepartmentModal()
    {
        editingDepartment = new Department();
        showDepartmentModal = true;
    }

    private void EditDepartment(Department dept)
    {
        editingDepartment = new Department
        {
            Id = dept.Id,
            Name = dept.Name,
            Description = dept.Description
        };
        showDepartmentModal = true;
    }

    private void CloseDepartmentModal()
    {
        showDepartmentModal = false;
        editingDepartment = new();
    }

    private async Task SaveDepartment()
    {
        try
        {
            if (editingDepartment.Id > 0)
            {
                var existing = await DbContext.Departments.FindAsync(editingDepartment.Id);
                if (existing != null)
                {
                    existing.Name = editingDepartment.Name;
                    existing.Description = editingDepartment.Description;
                    existing.UpdatedAt = DateTime.Now;
                }
            }
            else
            {
                editingDepartment.CreatedAt = DateTime.Now;
                DbContext.Departments.Add(editingDepartment);
            }

            await DbContext.SaveChangesAsync();
            await LoadDepartments();
            CloseDepartmentModal();
            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ الإدارة بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ: {ex.Message}");
        }
    }

    private async Task ConfirmDeleteDepartment(int id, string name)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل أنت متأكد من حذف الإدارة '{name}'؟");
        if (confirmed)
        {
            await DeleteDepartment(id);
        }
    }

    private async Task DeleteDepartment(int id)
    {
        try
        {
            var dept = await DbContext.Departments.FindAsync(id);
            if (dept != null)
            {
                DbContext.Departments.Remove(dept);
                await DbContext.SaveChangesAsync();
                await LoadDepartments();
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الإدارة بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ: {ex.Message}");
        }
    }

    // Position methods
    private void ShowAddPositionModal()
    {
        editingPosition = new Position();
        showPositionModal = true;
    }

    private void EditPosition(Position pos)
    {
        editingPosition = new Position
        {
            Id = pos.Id,
            Name = pos.Name,
            Description = pos.Description
        };
        showPositionModal = true;
    }

    private void ClosePositionModal()
    {
        showPositionModal = false;
        editingPosition = new();
    }

    private async Task SavePosition()
    {
        try
        {
            if (editingPosition.Id > 0)
            {
                var existing = await DbContext.Positions.FindAsync(editingPosition.Id);
                if (existing != null)
                {
                    existing.Name = editingPosition.Name;
                    existing.Description = editingPosition.Description;
                    existing.UpdatedAt = DateTime.Now;
                }
            }
            else
            {
                editingPosition.CreatedAt = DateTime.Now;
                DbContext.Positions.Add(editingPosition);
            }

            await DbContext.SaveChangesAsync();
            await LoadPositions();
            ClosePositionModal();
            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ المنصب بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ: {ex.Message}");
        }
    }

    private async Task ConfirmDeletePosition(int id, string name)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل أنت متأكد من حذف المنصب '{name}'؟");
        if (confirmed)
        {
            await DeletePosition(id);
        }
    }

    private async Task DeletePosition(int id)
    {
        try
        {
            var pos = await DbContext.Positions.FindAsync(id);
            if (pos != null)
            {
                DbContext.Positions.Remove(pos);
                await DbContext.SaveChangesAsync();
                await LoadPositions();
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المنصب بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ: {ex.Message}");
        }
    }
}
