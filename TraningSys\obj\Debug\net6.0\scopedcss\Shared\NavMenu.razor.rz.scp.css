.navbar-toggler[b-kail55l7wg] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-kail55l7wg] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-kail55l7wg] {
    font-size: 1.1rem;
}

.oi[b-kail55l7wg] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-kail55l7wg] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-kail55l7wg] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-kail55l7wg] {
        padding-bottom: 1rem;
    }

    .nav-item[b-kail55l7wg]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-kail55l7wg]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-kail55l7wg]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-kail55l7wg] {
        display: none;
    }

    .collapse[b-kail55l7wg] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}
