@page "/external-courses/details/{Id:int}"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation

<PageTitle>تفاصيل الدورة الخارجية</PageTitle>

@if (course == null)
{
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات الدورة...</p>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-university me-2"></i>تفاصيل الدورة الخارجية</h2>
        <div>
            <a href="/nominations/course/@Id" class="btn btn-success me-2">
                <i class="fas fa-users me-2"></i>إدارة الترشيحات
            </a>
            <a href="/external-courses/edit/@Id" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>تعديل
            </a>
            <a href="/external-courses" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- Course Overview Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <div class="course-icon bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-university"></i>
                    </div>
                    <h4 class="mb-1">@course.Name</h4>
                    <p class="text-muted mb-2">دورة خارجية</p>
                    
                    @switch (course.Status)
                    {
                        case CourseStatus.Scheduled:
                            <span class="badge bg-primary fs-6">مجدولة</span>
                            break;
                        case CourseStatus.InProgress:
                            <span class="badge bg-warning fs-6">جارية</span>
                            break;
                        case CourseStatus.Completed:
                            <span class="badge bg-success fs-6">مكتملة</span>
                            break;
                        case CourseStatus.Cancelled:
                            <span class="badge bg-danger fs-6">ملغية</span>
                            break;
                    }
                </div>
            </div>

            <!-- Course Statistics -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات الدورة</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 text-primary mb-1">@nominations.Count()</div>
                            <small class="text-muted">إجمالي المرشحين</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 text-success mb-1">@nominations.Count(n => n.Status == NominationStatus.Accepted)</div>
                            <small class="text-muted">المقبولين</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 text-warning mb-1">@nominations.Count(n => n.Status == NominationStatus.Excused)</div>
                            <small class="text-muted">المعتذرين</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 text-info mb-1">@nominations.Count(n => n.Status == NominationStatus.Completed)</div>
                            <small class="text-muted">المكتملين</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cost Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>معلومات التكلفة</h6>
                </div>
                <div class="card-body">
                    @if (course.Cost.HasValue)
                    {
                        <div class="text-center">
                            <div class="h3 text-success mb-1">@course.Cost.Value.ToString("N0") ريال</div>
                            <small class="text-muted">التكلفة الإجمالية</small>
                            
                            @if (course.MaxParticipants.HasValue && course.MaxParticipants > 0)
                            {
                                <hr />
                                <div class="h5 text-info mb-1">@((course.Cost.Value / course.MaxParticipants.Value).ToString("N0")) ريال</div>
                                <small class="text-muted">التكلفة لكل مشارك</small>
                            }
                            
                            @if (nominations.Count(n => n.Status == NominationStatus.Accepted) > 0)
                            {
                                <hr />
                                <div class="h5 text-warning mb-1">@((course.Cost.Value / Math.Max(1, nominations.Count(n => n.Status == NominationStatus.Accepted))).ToString("N0")) ريال</div>
                                <small class="text-muted">التكلفة الفعلية لكل مشارك</small>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted">
                            <i class="fas fa-question-circle fa-2x mb-2"></i>
                            <p>لم يتم تحديد التكلفة</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Course Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الدورة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">اسم الدورة</label>
                            <div class="fw-bold">@course.Name</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">نوع الدورة</label>
                            <div class="fw-bold">
                                <span class="badge bg-warning">خارجية</span>
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(course.Description))
                        {
                            <div class="col-md-12 mb-3">
                                <label class="form-label text-muted">وصف الدورة</label>
                                <div>@course.Description</div>
                            </div>
                        }
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ البداية</label>
                            <div class="fw-bold">@course.StartDate.ToString("dd/MM/yyyy")</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ النهاية</label>
                            <div class="fw-bold">@course.EndDate.ToString("dd/MM/yyyy")</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">مدة الدورة</label>
                            <div class="fw-bold">@course.DurationDays يوم</div>
                        </div>
                        @if (!string.IsNullOrEmpty(course.Location))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">مكان الدورة</label>
                                <div class="fw-bold">
                                    <i class="fas fa-map-marker-alt me-1 text-danger"></i>@course.Location
                                </div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(course.Instructor))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الجهة المنظمة</label>
                                <div class="fw-bold">@course.Instructor</div>
                            </div>
                        }
                        @if (course.MaxParticipants.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الحد الأقصى للمشاركين</label>
                                <div class="fw-bold">@course.MaxParticipants</div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(course.Notes))
                        {
                            <div class="col-md-12 mb-3">
                                <label class="form-label text-muted">ملاحظات</label>
                                <div class="alert alert-info">
                                    <i class="fas fa-sticky-note me-2"></i>@course.Notes
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Participants List -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-users me-2"></i>قائمة المرشحين</h6>
                    <a href="/nominations/create?courseId=@course.Id" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة مرشح
                    </a>
                </div>
                <div class="card-body">
                    @if (!nominations.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد ترشيحات لهذه الدورة</p>
                            <a href="/nominations/create?courseId=@course.Id" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة مرشح
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>الإدارة</th>
                                        <th>تاريخ الترشيح</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var nomination in nominations.Take(10))
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-warning text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                        @nomination.Employee.FirstName.Substring(0, 1)
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">@nomination.Employee.FullName</div>
                                                        <small class="text-muted">@nomination.Employee.EmployeeNumber</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@nomination.Employee.Department?.Name</td>
                                            <td>@nomination.NominationDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @switch (nomination.Status)
                                                {
                                                    case NominationStatus.Nominated:
                                                        <span class="badge bg-primary">مرشح</span>
                                                        break;
                                                    case NominationStatus.Accepted:
                                                        <span class="badge bg-success">مقبول</span>
                                                        break;
                                                    case NominationStatus.Rejected:
                                                        <span class="badge bg-danger">مرفوض</span>
                                                        break;
                                                    case NominationStatus.Excused:
                                                        <span class="badge bg-warning">معتذر</span>
                                                        break;
                                                    case NominationStatus.Completed:
                                                        <span class="badge bg-success">مكتمل</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <a href="/employees/details/@nomination.EmployeeId" class="btn btn-sm btn-outline-info" title="عرض الموظف">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        
                        @if (nominations.Count() > 10)
                        {
                            <div class="text-center mt-3">
                                <a href="/nominations/course/@course.Id" class="btn btn-outline-primary">
                                    عرض جميع المرشحين (@nominations.Count())
                                </a>
                            </div>
                        }
                    }
                </div>
            </div>

            <!-- System Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-cog me-2"></i>معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ الإنشاء</label>
                            <div>@course.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                        @if (course.UpdatedAt.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">آخر تحديث</label>
                                <div>@course.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public int Id { get; set; }
    
    private Course? course;
    private List<CourseNomination> nominations = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadCourse();
        if (course != null)
        {
            await LoadNominations();
        }
    }

    private async Task LoadCourse()
    {
        course = await DbContext.Courses
            .FirstOrDefaultAsync(c => c.Id == Id && c.Type == CourseType.External);

        if (course == null)
        {
            Navigation.NavigateTo("/external-courses");
        }
    }

    private async Task LoadNominations()
    {
        nominations = await DbContext.CourseNominations
            .Include(cn => cn.Employee)
            .ThenInclude(e => e.Department)
            .Where(cn => cn.CourseId == Id)
            .OrderByDescending(cn => cn.NominationDate)
            .ToListAsync();
    }
}
