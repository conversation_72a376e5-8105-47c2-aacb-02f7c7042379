@page "/nominations/create"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>إضافة ترشيح جديد</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus me-2"></i>إضافة ترشيح جديد</h2>
    <a href="/nominations" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-hand-point-up me-2"></i>بيانات الترشيح</h5>
            </div>
            <div class="card-body">
                <EditForm Model="nomination" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">الدورة <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="nomination.CourseId" class="form-select" @onchange="OnCourseChanged">
                                <option value="0">-- اختر الدورة --</option>
                                @if (courses != null)
                                {
                                    @foreach (var course in courses)
                                    {
                                        <option value="@course.Id">
                                            @course.Name - @course.StartDate.ToString("dd/MM/yyyy") 
                                            (@(course.Type == CourseType.Internal ? "داخلية" : "خارجية"))
                                        </option>
                                    }
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => nomination.CourseId)" class="text-danger" />
                        </div>
                    </div>

                    @if (selectedCourse != null)
                    {
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات الدورة المختارة:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>اسم الدورة:</strong> @selectedCourse.Name<br />
                                    <strong>تاريخ البداية:</strong> @selectedCourse.StartDate.ToString("dd/MM/yyyy")<br />
                                    <strong>تاريخ النهاية:</strong> @selectedCourse.EndDate.ToString("dd/MM/yyyy")
                                </div>
                                <div class="col-md-6">
                                    <strong>نوع الدورة:</strong> @(selectedCourse.Type == CourseType.Internal ? "داخلية" : "خارجية")<br />
                                    <strong>المدة:</strong> @selectedCourse.DurationDays يوم<br />
                                    @if (selectedCourse.MaxParticipants.HasValue)
                                    {
                                        <strong>الحد الأقصى:</strong> @selectedCourse.MaxParticipants مشارك<br />
                                    }
                                </div>
                            </div>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">الموظفين المرشحين <span class="text-danger">*</span></label>
                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                @if (employees != null)
                                {
                                    @foreach (var employee in employees)
                                    {
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" 
                                                   @onchange="@((e) => ToggleEmployee(employee.Id, (bool)e.Value!))"
                                                   id="<EMAIL>" />
                                            <label class="form-check-label" for="<EMAIL>">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                        @employee.FirstName.Substring(0, 1)
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">@employee.FullName</div>
                                                        <small class="text-muted">@employee.EmployeeNumber - @employee.Department?.Name</small>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    }
                                }
                            </div>
                            @if (selectedEmployeeIds.Count == 0)
                            {
                                <div class="text-danger mt-1">يرجى اختيار موظف واحد على الأقل</div>
                            }
                            else
                            {
                                <div class="text-success mt-1">تم اختيار @selectedEmployeeIds.Count موظف</div>
                            }
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الترشيح <span class="text-danger">*</span></label>
                            <InputDate @bind-Value="nomination.NominationDate" class="form-control" />
                            <ValidationMessage For="@(() => nomination.NominationDate)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة الترشيح <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="nomination.Status" class="form-select">
                                <option value="@NominationStatus.Nominated">مرشح</option>
                                <option value="@NominationStatus.Accepted">مقبول</option>
                                <option value="@NominationStatus.Rejected">مرفوض</option>
                                <option value="@NominationStatus.Excused">معتذر</option>
                                <option value="@NominationStatus.Completed">مكتمل</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => nomination.Status)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">ملاحظات</label>
                            <InputTextArea @bind-Value="nomination.Notes" class="form-control" rows="3" placeholder="أي ملاحظات حول الترشيح" />
                            <ValidationMessage For="@(() => nomination.Notes)" class="text-danger" />
                        </div>
                    </div>

                    @if (selectedCourse?.Type == CourseType.External)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> هذه دورة خارجية وقد تتطلب موافقات إضافية وتكاليف سفر.
                        </div>
                    }

                    <div class="d-flex justify-content-end gap-2">
                        <a href="/nominations" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success" disabled="@(isSubmitting || selectedEmployeeIds.Count == 0)">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save me-2"></i>
                                <span>حفظ الترشيحات (@selectedEmployeeIds.Count)</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private CourseNomination nomination = new CourseNomination 
    { 
        Status = NominationStatus.Nominated,
        NominationDate = DateTime.Today
    };
    private List<Course>? courses;
    private List<Employee>? employees;
    private Course? selectedCourse;
    private HashSet<int> selectedEmployeeIds = new();
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCourses();
        await LoadEmployees();
    }

    private async Task LoadCourses()
    {
        courses = await DbContext.Courses
            .Where(c => c.Status == CourseStatus.Scheduled || c.Status == CourseStatus.InProgress)
            .OrderBy(c => c.StartDate)
            .ToListAsync();
    }

    private async Task LoadEmployees()
    {
        employees = await DbContext.Employees
            .Include(e => e.Department)
            .Where(e => e.IsActive)
            .OrderBy(e => e.FirstName)
            .ThenBy(e => e.LastName)
            .ToListAsync();
    }

    private async Task OnCourseChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int courseId) && courseId > 0)
        {
            selectedCourse = await DbContext.Courses.FindAsync(courseId);
            
            // Clear previous selections when course changes
            selectedEmployeeIds.Clear();
            
            // Filter out employees already nominated for this course
            if (selectedCourse != null)
            {
                var alreadyNominated = await DbContext.CourseNominations
                    .Where(n => n.CourseId == courseId)
                    .Select(n => n.EmployeeId)
                    .ToListAsync();
                
                employees = await DbContext.Employees
                    .Include(e => e.Department)
                    .Where(e => e.IsActive && !alreadyNominated.Contains(e.Id))
                    .OrderBy(e => e.FirstName)
                    .ThenBy(e => e.LastName)
                    .ToListAsync();
            }
        }
        else
        {
            selectedCourse = null;
            selectedEmployeeIds.Clear();
            await LoadEmployees();
        }
    }

    private void ToggleEmployee(int employeeId, bool isSelected)
    {
        if (isSelected)
        {
            selectedEmployeeIds.Add(employeeId);
        }
        else
        {
            selectedEmployeeIds.Remove(employeeId);
        }
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            // Validate course selection
            if (nomination.CourseId == 0)
            {
                await JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار الدورة.");
                isSubmitting = false;
                return;
            }

            // Validate employee selection
            if (selectedEmployeeIds.Count == 0)
            {
                await JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار موظف واحد على الأقل.");
                isSubmitting = false;
                return;
            }

            // Check course capacity if applicable
            if (selectedCourse?.MaxParticipants.HasValue == true)
            {
                var currentNominations = await DbContext.CourseNominations
                    .CountAsync(n => n.CourseId == nomination.CourseId && 
                                    (n.Status == NominationStatus.Nominated || n.Status == NominationStatus.Accepted));
                
                if (currentNominations + selectedEmployeeIds.Count > selectedCourse.MaxParticipants.Value)
                {
                    await JSRuntime.InvokeVoidAsync("alert", 
                        $"عدد المرشحين ({selectedEmployeeIds.Count}) يتجاوز السعة المتاحة للدورة. " +
                        $"السعة المتاحة: {selectedCourse.MaxParticipants.Value - currentNominations}");
                    isSubmitting = false;
                    return;
                }
            }

            // Create nominations for all selected employees
            var nominations = selectedEmployeeIds.Select(employeeId => new CourseNomination
            {
                EmployeeId = employeeId,
                CourseId = nomination.CourseId,
                NominationDate = nomination.NominationDate,
                Status = nomination.Status,
                Notes = nomination.Notes
            }).ToList();

            DbContext.CourseNominations.AddRange(nominations);
            await DbContext.SaveChangesAsync();

            await JSRuntime.InvokeVoidAsync("alert", $"تم إضافة {selectedEmployeeIds.Count} ترشيح بنجاح!");
            Navigation.NavigateTo("/nominations");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
