﻿@page "/"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject ApplicationDbContext DbContext

<PageTitle>الرئيسية</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="welcome-section mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5" style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%); color: white;">
                        <i class="fas fa-graduation-cap fa-4x mb-3"></i>
                        <h1 class="display-4 fw-bold">مرحباً بك في تطوير</h1>
                        <p class="lead">نظام شامل لإدارة التدريب والتطوير المهني</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <AuthorizeView>
        <Authorized>
            <div class="row g-4 mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                            <h5 class="card-title">إدارة الموظفين</h5>
                            <p class="card-text text-muted">إضافة وتعديل بيانات الموظفين وإدارة ملفاتهم الشخصية</p>
                            <a href="/employees" class="btn btn-outline-primary">عرض الموظفين</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-success bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-chalkboard-teacher fa-2x"></i>
                            </div>
                            <h5 class="card-title">الدورات التدريبية</h5>
                            <p class="card-text text-muted">إدارة الدورات الداخلية والخارجية وترشيح المشاركين</p>
                            <a href="/internal-courses" class="btn btn-outline-success">عرض الدورات</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-info bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-certificate fa-2x"></i>
                            </div>
                            <h5 class="card-title">الشهادات</h5>
                            <p class="card-text text-muted">إصدار وطباعة شهادات إتمام الدورات التدريبية</p>
                            <a href="/certificates" class="btn btn-outline-info">عرض الشهادات</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-warning bg-gradient text-white rounded-circle mb-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-chart-bar fa-2x"></i>
                            </div>
                            <h5 class="card-title">التقارير</h5>
                            <p class="card-text text-muted">تقارير شاملة عن الدورات والمشاركين والإحصائيات</p>
                            <a href="/reports" class="btn btn-outline-warning">عرض التقارير</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h3 class="mb-1">@totalEmployees</h3>
                            <p class="mb-0">إجمالي الموظفين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                            <h3 class="mb-1">@totalCourses</h3>
                            <p class="mb-0">إجمالي الدورات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-play fa-2x mb-2"></i>
                            <h3 class="mb-1">@activeCourses</h3>
                            <p class="mb-0">الدورات الجارية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-certificate fa-2x mb-2"></i>
                            <h3 class="mb-1">@totalCertificates</h3>
                            <p class="mb-0">الشهادات المصدرة</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><i class="fas fa-calendar-alt me-2"></i>الدورات الجارية</h5>
                            <a href="/internal-courses" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            @if (currentCourses?.Any() == true)
                            {
                                @foreach (var course in currentCourses.Take(5))
                                {
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                        <div>
                                            <div class="fw-bold">@course.Name</div>
                                            <small class="text-muted">
                                                @course.StartDate.ToString("dd/MM/yyyy") - @course.EndDate.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                        <span class="badge bg-warning">جارية</span>
                                    </div>
                                }
                            }
                            else
                            {
                                <p class="text-muted">لا توجد دورات جارية حالياً</p>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><i class="fas fa-clock me-2"></i>الدورات القادمة</h5>
                            <a href="/internal-courses" class="btn btn-sm btn-outline-success">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            @if (upcomingCourses?.Any() == true)
                            {
                                @foreach (var course in upcomingCourses.Take(5))
                                {
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                        <div>
                                            <div class="fw-bold">@course.Name</div>
                                            <small class="text-muted">
                                                @course.StartDate.ToString("dd/MM/yyyy") - @course.EndDate.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                        <span class="badge bg-primary">مجدولة</span>
                                    </div>
                                }
                            }
                            else
                            {
                                <p class="text-muted">لا توجد دورات مجدولة قريباً</p>
                                <a href="/internal-courses/create" class="btn btn-sm btn-primary">إضافة دورة جديدة</a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </Authorized>
        <NotAuthorized>
            <div class="container-fluid vh-100 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);">
                <div class="text-center text-white">
                    <div class="spinner-border mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p>جاري توجيهك لصفحة تسجيل الدخول...</p>
                </div>
            </div>
        </NotAuthorized>
    </AuthorizeView>
</div>

@code {
    private int totalEmployees = 0;
    private int totalCourses = 0;
    private int activeCourses = 0;
    private int totalCertificates = 0;
    private List<Course>? currentCourses;
    private List<Course>? upcomingCourses;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (!authState.User.Identity?.IsAuthenticated ?? true)
        {
            Navigation.NavigateTo("/Identity/Account/Login", true);
            return;
        }

        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            // Load statistics with simpler queries
            totalEmployees = await DbContext.Employees.Where(e => e.IsActive).CountAsync();
            totalCourses = await DbContext.Courses.CountAsync();
            activeCourses = 0; // Simplified for now
            totalCertificates = 0;

            // Load current courses (simplified)
            currentCourses = new List<Course>();

            // Load upcoming courses (simplified)
            upcomingCourses = new List<Course>();
        }
        catch (Exception ex)
        {
            // Handle error silently for now
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");

            // Set default values
            totalEmployees = 0;
            totalCourses = 0;
            activeCourses = 0;
            totalCertificates = 0;
            currentCourses = new List<Course>();
            upcomingCourses = new List<Course>();
        }
    }
}
