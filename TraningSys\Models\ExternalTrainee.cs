using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TraningSys.Models
{
    public class ExternalTrainee
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأوسط مطلوب")]
        [Display(Name = "الاسم الأوسط")]
        [StringLength(50)]
        public string MiddleName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [Display(Name = "الاسم الأخير")]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Display(Name = "الاسم الكامل")]
        public string FullName => $"{FirstName} {MiddleName} {LastName}";

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "رقم الهاتف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? PhoneNumber { get; set; }

        [Required(ErrorMessage = "الجهة مطلوبة")]
        [Display(Name = "الجهة")]
        [StringLength(200)]
        public string Organization { get; set; } = string.Empty;

        [Display(Name = "المنصب")]
        [StringLength(100)]
        public string? Position { get; set; }

        [Required(ErrorMessage = "الدورة مطلوبة")]
        [Display(Name = "الدورة")]
        public int CourseId { get; set; }

        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; } = null!;

        [Display(Name = "تاريخ التسجيل")]
        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        [Display(Name = "حضر الدورة")]
        public bool Attended { get; set; } = false;

        [Display(Name = "اكتمل التدريب")]
        public bool Completed { get; set; } = false;

        [Display(Name = "ملاحظات")]
        [StringLength(1000)]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Certificate> Certificates { get; set; } = new List<Certificate>();
    }
}
