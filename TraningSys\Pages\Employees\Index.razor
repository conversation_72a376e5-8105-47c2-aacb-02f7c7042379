@page "/employees"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>إدارة الموظفين</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users me-2"></i>إدارة الموظفين</h2>
    <a href="/employees/create" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>إضافة موظف جديد
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">قائمة الموظفين</h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في الموظفين..." 
                           @bind="searchTerm" @onkeyup="SearchEmployees" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="SearchEmployees">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        @if (employees == null)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>
        }
        else if (!employees.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد موظفين</h5>
                <p class="text-muted">ابدأ بإضافة موظف جديد</p>
                <a href="/employees/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة موظف
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الموظف</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>الإدارة</th>
                            <th>المنصب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in employees)
                        {
                            <tr>
                                <td>
                                    <strong>@employee.EmployeeNumber</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                            @employee.FirstName.Substring(0, 1)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@employee.FullName</div>
                                            @if (employee.PhoneNumber != null)
                                            {
                                                <small class="text-muted">@employee.PhoneNumber</small>
                                            }
                                        </div>
                                    </div>
                                </td>
                                <td>@employee.Email</td>
                                <td>@employee.Department?.Name</td>
                                <td>@employee.Position?.Name</td>
                                <td>
                                    @if (employee.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">غير نشط</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/employees/details/@employee.Id" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/employees/edit/@employee.Id" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" title="حذف" 
                                                @onclick="() => ConfirmDelete(employee.Id, employee.FullName)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض @employees.Count() من أصل @totalCount موظف
                    </small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage - 1)">السابق</button>
                        </li>
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => LoadPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage + 1)">التالي</button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    </div>
</div>

@code {
    private List<Employee>? employees;
    private string searchTerm = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployees();
    }

    private async Task LoadEmployees()
    {
        var query = DbContext.Employees
            .Include(e => e.Department)
            .Include(e => e.Position)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(e => 
                e.FirstName.Contains(searchTerm) ||
                e.MiddleName.Contains(searchTerm) ||
                e.LastName.Contains(searchTerm) ||
                e.EmployeeNumber.Contains(searchTerm) ||
                e.Email.Contains(searchTerm));
        }

        totalCount = await query.CountAsync();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        employees = await query
            .OrderBy(e => e.EmployeeNumber)
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private async Task SearchEmployees()
    {
        currentPage = 1;
        await LoadEmployees();
    }

    private async Task LoadPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadEmployees();
        }
    }

    private async Task ConfirmDelete(int employeeId, string employeeName)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"هل أنت متأكد من حذف الموظف '{employeeName}'؟\nهذا الإجراء لا يمكن التراجع عنه.");
        
        if (confirmed)
        {
            await DeleteEmployee(employeeId);
        }
    }

    private async Task DeleteEmployee(int employeeId)
    {
        try
        {
            var employee = await DbContext.Employees.FindAsync(employeeId);
            if (employee != null)
            {
                DbContext.Employees.Remove(employee);
                await DbContext.SaveChangesAsync();
                await LoadEmployees();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الموظف بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حذف الموظف: {ex.Message}");
        }
    }
}
