using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace TraningSys.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [Display(Name = "الاسم الأخير")]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Display(Name = "الاسم الكامل")]
        public string FullName => $"{FirstName} {LastName}";

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }
    }
}
