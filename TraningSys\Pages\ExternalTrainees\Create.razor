@page "/external-trainees/create"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>إضافة متدرب خارجي جديد</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus me-2"></i>إضافة متدرب خارجي جديد</h2>
    <a href="/external-trainees" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-user-friends me-2"></i>بيانات المتدرب الخارجي</h5>
            </div>
            <div class="card-body">
                <EditForm Model="trainee" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                            <InputText @bind-Value="trainee.FirstName" class="form-control" placeholder="الاسم الأول" />
                            <ValidationMessage For="@(() => trainee.FirstName)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                            <InputText @bind-Value="trainee.LastName" class="form-control" placeholder="الاسم الأخير" />
                            <ValidationMessage For="@(() => trainee.LastName)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهوية الوطنية</label>
                            <InputText @bind-Value="trainee.NationalId" class="form-control" placeholder="1234567890" />
                            <ValidationMessage For="@(() => trainee.NationalId)" class="text-danger" />
                            <div class="form-text">رقم الهوية الوطنية أو الإقامة</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <InputText @bind-Value="trainee.Email" class="form-control" placeholder="<EMAIL>" />
                            <ValidationMessage For="@(() => trainee.Email)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <InputText @bind-Value="trainee.Phone" class="form-control" placeholder="05xxxxxxxx" />
                            <ValidationMessage For="@(() => trainee.Phone)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الميلاد</label>
                            <InputDate @bind-Value="trainee.DateOfBirth" class="form-control" />
                            <ValidationMessage For="@(() => trainee.DateOfBirth)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الجهة/المؤسسة <span class="text-danger">*</span></label>
                            <InputText @bind-Value="trainee.Organization" class="form-control" placeholder="اسم الجهة أو المؤسسة" />
                            <ValidationMessage For="@(() => trainee.Organization)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المنصب</label>
                            <InputText @bind-Value="trainee.Position" class="form-control" placeholder="المنصب الوظيفي" />
                            <ValidationMessage For="@(() => trainee.Position)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المؤهل العلمي</label>
                            <InputSelect @bind-Value="trainee.EducationLevel" class="form-select">
                                <option value="">-- اختر المؤهل --</option>
                                <option value="@EducationLevel.HighSchool">ثانوية عامة</option>
                                <option value="@EducationLevel.Diploma">دبلوم</option>
                                <option value="@EducationLevel.Bachelor">بكالوريوس</option>
                                <option value="@EducationLevel.HighDiploma">دبلوم عالي</option>
                                <option value="@EducationLevel.Master">ماجستير</option>
                                <option value="@EducationLevel.PhD">دكتوراه</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => trainee.EducationLevel)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">التخصص</label>
                            <InputText @bind-Value="trainee.Specialization" class="form-control" placeholder="التخصص الأكاديمي" />
                            <ValidationMessage For="@(() => trainee.Specialization)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">سنوات الخبرة</label>
                            <InputNumber @bind-Value="trainee.YearsOfExperience" class="form-control" placeholder="0" />
                            <ValidationMessage For="@(() => trainee.YearsOfExperience)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحالة <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="trainee.IsActive" class="form-select">
                                <option value="true">نشط</option>
                                <option value="false">غير نشط</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => trainee.IsActive)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">العنوان</label>
                            <InputTextArea @bind-Value="trainee.Address" class="form-control" rows="2" placeholder="العنوان الكامل" />
                            <ValidationMessage For="@(() => trainee.Address)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">ملاحظات</label>
                            <InputTextArea @bind-Value="trainee.Notes" class="form-control" rows="3" placeholder="أي ملاحظات إضافية حول المتدرب" />
                            <ValidationMessage For="@(() => trainee.Notes)" class="text-danger" />
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومة:</strong> المتدربين الخارجيين هم أشخاص من خارج الوزارة يحضرون الدورات التدريبية.
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="/external-trainees" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save me-2"></i>
                                <span>حفظ المتدرب</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private ExternalTrainee trainee = new ExternalTrainee 
    { 
        IsActive = true,
        RegistrationDate = DateTime.Today
    };
    private bool isSubmitting = false;

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            // Check if national ID already exists (if provided)
            if (!string.IsNullOrWhiteSpace(trainee.NationalId))
            {
                var existingNationalId = await DbContext.ExternalTrainees
                    .FirstOrDefaultAsync(t => t.NationalId == trainee.NationalId);
                
                if (existingNationalId != null)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "رقم الهوية الوطنية موجود مسبقاً. يرجى التحقق من البيانات.");
                    isSubmitting = false;
                    return;
                }
            }

            // Check if email already exists (if provided)
            if (!string.IsNullOrWhiteSpace(trainee.Email))
            {
                var existingEmail = await DbContext.ExternalTrainees
                    .FirstOrDefaultAsync(t => t.Email == trainee.Email);
                
                if (existingEmail != null)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "البريد الإلكتروني موجود مسبقاً. يرجى استخدام بريد آخر.");
                    isSubmitting = false;
                    return;
                }
            }

            trainee.RegistrationDate = DateTime.Now;
            DbContext.ExternalTrainees.Add(trainee);
            await DbContext.SaveChangesAsync();

            await JSRuntime.InvokeVoidAsync("alert", "تم إضافة المتدرب الخارجي بنجاح!");
            Navigation.NavigateTo("/external-trainees");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
