@page "/nominations"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>إدارة الترشيحات</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-hand-point-up me-2"></i>إدارة الترشيحات</h2>
    <a href="/nominations/create" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>إضافة ترشيح جديد
    </a>
</div>

<!-- Filter Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4 class="mb-1">@totalNominations</h4>
                <p class="mb-0 text-muted">إجمالي الترشيحات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="mb-1">@pendingNominations</h4>
                <p class="mb-0 text-muted">في الانتظار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-check fa-2x text-success mb-2"></i>
                <h4 class="mb-1">@acceptedNominations</h4>
                <p class="mb-0 text-muted">مقبولة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-times fa-2x text-danger mb-2"></i>
                <h4 class="mb-1">@rejectedNominations</h4>
                <p class="mb-0 text-muted">مرفوضة</p>
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h5 class="mb-0">قائمة الترشيحات</h5>
            </div>
            <div class="col-md-4">
                <select class="form-select" @bind="selectedStatus" @onchange="FilterByStatus">
                    <option value="">جميع الحالات</option>
                    <option value="@NominationStatus.Nominated">مرشح</option>
                    <option value="@NominationStatus.Accepted">مقبول</option>
                    <option value="@NominationStatus.Rejected">مرفوض</option>
                    <option value="@NominationStatus.Excused">معتذر</option>
                    <option value="@NominationStatus.Completed">مكتمل</option>
                </select>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في الترشيحات..." 
                           @bind="searchTerm" @onkeyup="SearchNominations" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="SearchNominations">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        @if (nominations == null)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>
        }
        else if (!nominations.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-hand-point-up fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد ترشيحات</h5>
                <p class="text-muted">ابدأ بإضافة ترشيح جديد</p>
                <a href="/nominations/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة ترشيح
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الموظف</th>
                            <th>الدورة</th>
                            <th>نوع الدورة</th>
                            <th>تاريخ الترشيح</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var nomination in nominations)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                            @nomination.Employee.FirstName.Substring(0, 1)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@nomination.Employee.FullName</div>
                                            <small class="text-muted">@nomination.Employee.EmployeeNumber</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">@nomination.Course.Name</div>
                                        <small class="text-muted">
                                            @nomination.Course.StartDate.ToString("dd/MM/yyyy") - @nomination.Course.EndDate.ToString("dd/MM/yyyy")
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    @if (nomination.Course.Type == CourseType.Internal)
                                    {
                                        <span class="badge bg-info">داخلية</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">خارجية</span>
                                    }
                                </td>
                                <td>@nomination.NominationDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    @switch (nomination.Status)
                                    {
                                        case NominationStatus.Nominated:
                                            <span class="badge bg-primary">مرشح</span>
                                            break;
                                        case NominationStatus.Accepted:
                                            <span class="badge bg-success">مقبول</span>
                                            break;
                                        case NominationStatus.Rejected:
                                            <span class="badge bg-danger">مرفوض</span>
                                            break;
                                        case NominationStatus.Excused:
                                            <span class="badge bg-warning">معتذر</span>
                                            break;
                                        case NominationStatus.Completed:
                                            <span class="badge bg-info">مكتمل</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(nomination.Notes))
                                    {
                                        <span title="@nomination.Notes" class="text-truncate d-inline-block" style="max-width: 100px;">
                                            @nomination.Notes
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-success" title="قبول" 
                                                @onclick="() => UpdateStatus(nomination.Id, NominationStatus.Accepted)"
                                                disabled="@(nomination.Status == NominationStatus.Accepted)">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="رفض" 
                                                @onclick="() => UpdateStatus(nomination.Id, NominationStatus.Rejected)"
                                                disabled="@(nomination.Status == NominationStatus.Rejected)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" title="اعتذار" 
                                                @onclick="() => UpdateStatus(nomination.Id, NominationStatus.Excused)"
                                                disabled="@(nomination.Status == NominationStatus.Excused)">
                                            <i class="fas fa-exclamation"></i>
                                        </button>
                                        <a href="/nominations/edit/@nomination.Id" class="btn btn-sm btn-outline-info" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-secondary" title="حذف" 
                                                @onclick="() => ConfirmDelete(nomination.Id, nomination.Employee.FullName)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض @nominations.Count() من أصل @totalCount ترشيح
                    </small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage - 1)">السابق</button>
                        </li>
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => LoadPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage + 1)">التالي</button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    </div>
</div>

@code {
    private List<CourseNomination>? nominations;
    private string searchTerm = "";
    private string selectedStatus = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages = 0;
    
    // Statistics
    private int totalNominations = 0;
    private int pendingNominations = 0;
    private int acceptedNominations = 0;
    private int rejectedNominations = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
        await LoadNominations();
    }

    private async Task LoadStatistics()
    {
        totalNominations = await DbContext.CourseNominations.CountAsync();
        pendingNominations = await DbContext.CourseNominations.CountAsync(n => n.Status == NominationStatus.Nominated);
        acceptedNominations = await DbContext.CourseNominations.CountAsync(n => n.Status == NominationStatus.Accepted);
        rejectedNominations = await DbContext.CourseNominations.CountAsync(n => n.Status == NominationStatus.Rejected);
    }

    private async Task LoadNominations()
    {
        var query = DbContext.CourseNominations
            .Include(n => n.Employee)
            .Include(n => n.Course)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(n => 
                n.Employee.FirstName.Contains(searchTerm) ||
                n.Employee.LastName.Contains(searchTerm) ||
                n.Employee.EmployeeNumber.Contains(searchTerm) ||
                n.Course.Name.Contains(searchTerm));
        }

        if (!string.IsNullOrWhiteSpace(selectedStatus) && Enum.TryParse<NominationStatus>(selectedStatus, out var status))
        {
            query = query.Where(n => n.Status == status);
        }

        totalCount = await query.CountAsync();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        nominations = await query
            .OrderByDescending(n => n.NominationDate)
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private async Task SearchNominations()
    {
        currentPage = 1;
        await LoadNominations();
    }

    private async Task FilterByStatus()
    {
        currentPage = 1;
        await LoadNominations();
    }

    private async Task LoadPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadNominations();
        }
    }

    private async Task UpdateStatus(int nominationId, NominationStatus newStatus)
    {
        try
        {
            var nomination = await DbContext.CourseNominations.FindAsync(nominationId);
            if (nomination != null)
            {
                nomination.Status = newStatus;
                await DbContext.SaveChangesAsync();
                await LoadStatistics();
                await LoadNominations();
                
                string statusText = newStatus switch
                {
                    NominationStatus.Accepted => "قبول",
                    NominationStatus.Rejected => "رفض",
                    NominationStatus.Excused => "اعتذار",
                    _ => "تحديث"
                };
                
                await JSRuntime.InvokeVoidAsync("alert", $"تم {statusText} الترشيح بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء تحديث الحالة: {ex.Message}");
        }
    }

    private async Task ConfirmDelete(int nominationId, string employeeName)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"هل أنت متأكد من حذف ترشيح الموظف '{employeeName}'؟\nهذا الإجراء لا يمكن التراجع عنه.");
        
        if (confirmed)
        {
            await DeleteNomination(nominationId);
        }
    }

    private async Task DeleteNomination(int nominationId)
    {
        try
        {
            var nomination = await DbContext.CourseNominations.FindAsync(nominationId);
            if (nomination != null)
            {
                DbContext.CourseNominations.Remove(nomination);
                await DbContext.SaveChangesAsync();
                await LoadStatistics();
                await LoadNominations();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الترشيح بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حذف الترشيح: {ex.Message}");
        }
    }
}
