﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization

<PageTitle>نظام التدريب</PageTitle>

<CascadingAuthenticationState>

<div class="d-flex" style="min-height: 100vh;">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="logo-section">
            <i class="fas fa-graduation-cap"></i>
            <h4>نظام التدريب</h4>
        </div>

        <NavMenu />
    </nav>

    <!-- Main Content -->
    <div class="main-content" style="flex: 1;">
        <!-- Top Navbar -->
        <nav class="top-navbar">
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-secondary mobile-toggle me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">نظام التدريب</h5>
            </div>

            <div class="user-info">
                <AuthorizeView>
                    <Authorized>
                        <div class="user-avatar">
                            @context.User.Identity?.Name?.Substring(0, 1).ToUpper()
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link dropdown-toggle text-decoration-none" type="button" data-bs-toggle="dropdown">
                                @context.User.Identity?.Name
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="/Account/Manage"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="/Identity/Account/Logout">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </Authorized>
                    <NotAuthorized>
                        <a href="/Identity/Account/Login" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </NotAuthorized>
                </AuthorizeView>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-wrapper">
            @Body
        </div>
    </div>
</div>

<script>
    function toggleSidebar() {
        document.getElementById('sidebar').classList.toggle('show');
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggle = document.querySelector('.mobile-toggle');

        if (window.innerWidth <= 768 &&
            !sidebar.contains(event.target) &&
            !toggle.contains(event.target)) {
            sidebar.classList.remove('show');
        }
    });
</script>

</CascadingAuthenticationState>
