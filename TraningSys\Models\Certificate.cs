using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TraningSys.Models
{
    public class Certificate
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "رقم الشهادة مطلوب")]
        [Display(Name = "رقم الشهادة")]
        [StringLength(50)]
        public string CertificateNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الدورة مطلوبة")]
        [Display(Name = "الدورة")]
        public int CourseId { get; set; }

        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; } = null!;

        [Display(Name = "الموظف")]
        public int? EmployeeId { get; set; }

        [ForeignKey("EmployeeId")]
        public virtual Employee? Employee { get; set; }

        [Display(Name = "المتدرب الخارجي")]
        public int? ExternalTraineeId { get; set; }

        [ForeignKey("ExternalTraineeId")]
        public virtual ExternalTrainee? ExternalTrainee { get; set; }

        [Required(ErrorMessage = "تاريخ الإصدار مطلوب")]
        [Display(Name = "تاريخ الإصدار")]
        [DataType(DataType.Date)]
        public DateTime IssueDate { get; set; } = DateTime.Now;

        [Display(Name = "صالح حتى")]
        [DataType(DataType.Date)]
        public DateTime? ExpiryDate { get; set; }

        [Display(Name = "الدرجة")]
        [StringLength(20)]
        public string? Grade { get; set; }

        [Display(Name = "ملاحظات")]
        [StringLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "مطبوعة")]
        public bool IsPrinted { get; set; } = false;

        [Display(Name = "تاريخ الطباعة")]
        public DateTime? PrintDate { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Computed Properties
        [Display(Name = "اسم المتدرب")]
        public string TraineeName
        {
            get
            {
                if (Employee != null)
                    return Employee.FullName;
                if (ExternalTrainee != null)
                    return ExternalTrainee.FullName;
                return "غير محدد";
            }
        }
    }
}
