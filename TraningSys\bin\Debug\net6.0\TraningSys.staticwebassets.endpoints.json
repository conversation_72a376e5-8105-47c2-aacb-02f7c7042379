{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Identity/css/site.css", "AssetFile": "Identity/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1378"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TjhuPxIaovHDthInF3L1YN67qxqghzNA1py01oh2FJA=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TjhuPxIaovHDthInF3L1YN67qxqghzNA1py01oh2FJA="}]}, {"Route": "Identity/favicon.ico", "AssetFile": "Identity/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32038"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "Identity/js/site.js", "AssetFile": "Identity/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="}]}, {"Route": "Identity/lib/bootstrap/LICENSE", "AssetFile": "Identity/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5867"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos="}]}, {"Route": "Identity/lib/jquery-validation/LICENSE.md", "AssetFile": "Identity/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "Identity/lib/jquery/LICENSE.txt", "AssetFile": "Identity/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1641"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "Identity/lib/jquery/dist/jquery.js", "AssetFile": "Identity/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "287630"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.js", "AssetFile": "Identity/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89476"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.map", "AssetFile": "Identity/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "137974"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 21:19:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA="}]}, {"Route": "TraningSys.oww1jylre9.styles.css", "AssetFile": "TraningSys.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2841"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2CcBjab4VYM4bNlcUOlMRR8rGMNdSC1xsAjltbOWLIc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:55:36 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oww1jylre9"}, {"Name": "integrity", "Value": "sha256-2CcBjab4VYM4bNlcUOlMRR8rGMNdSC1xsAjltbOWLIc="}, {"Name": "label", "Value": "TraningSys.styles.css"}]}, {"Route": "TraningSys.styles.css", "AssetFile": "TraningSys.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2841"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2CcBjab4VYM4bNlcUOlMRR8rGMNdSC1xsAjltbOWLIc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:55:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2CcBjab4VYM4bNlcUOlMRR8rGMNdSC1xsAjltbOWLIc="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}]}, {"Route": "css/open-iconic/README.8h4oiah9s0.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8h4oiah9s0"}, {"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}, {"Name": "label", "Value": "css/open-iconic/README.md"}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/site.1fpfb6x0uo.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1fpfb6x0uo"}, {"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 17:49:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}]}