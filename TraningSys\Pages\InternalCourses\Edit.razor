@page "/internal-courses/edit/{Id:int}"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>تعديل الدورة الداخلية</PageTitle>

@if (course == null)
{
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات الدورة...</p>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-edit me-2"></i>تعديل الدورة: @course.Name</h2>
        <div>
            <a href="/internal-courses/details/@Id" class="btn btn-outline-info me-2">
                <i class="fas fa-eye me-2"></i>عرض التفاصيل
            </a>
            <a href="/internal-courses" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>تعديل بيانات الدورة</h5>
                </div>
                <div class="card-body">
                    <EditForm Model="course" OnValidSubmit="HandleValidSubmit">
                        <DataAnnotationsValidator />
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">اسم الدورة <span class="text-danger">*</span></label>
                                <InputText @bind-Value="course.Name" class="form-control" placeholder="أدخل اسم الدورة" />
                                <ValidationMessage For="@(() => course.Name)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">وصف الدورة</label>
                                <InputTextArea @bind-Value="course.Description" class="form-control" rows="3" placeholder="وصف مختصر عن الدورة وأهدافها" />
                                <ValidationMessage For="@(() => course.Description)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                                <InputDate @bind-Value="course.StartDate" class="form-control" />
                                <ValidationMessage For="@(() => course.StartDate)" class="text-danger" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ النهاية <span class="text-danger">*</span></label>
                                <InputDate @bind-Value="course.EndDate" class="form-control" />
                                <ValidationMessage For="@(() => course.EndDate)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مكان الدورة</label>
                                <InputText @bind-Value="course.Location" class="form-control" placeholder="قاعة التدريب الرئيسية" />
                                <ValidationMessage For="@(() => course.Location)" class="text-danger" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المدرب</label>
                                <InputText @bind-Value="course.Instructor" class="form-control" placeholder="اسم المدرب أو الجهة المدربة" />
                                <ValidationMessage For="@(() => course.Instructor)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأقصى للمشاركين</label>
                                <InputNumber @bind-Value="course.MaxParticipants" class="form-control" placeholder="20" />
                                <ValidationMessage For="@(() => course.MaxParticipants)" class="text-danger" />
                                <div class="form-text">اتركه فارغاً إذا لم يكن هناك حد أقصى</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">التكلفة (ريال)</label>
                                <InputNumber @bind-Value="course.Cost" class="form-control" placeholder="0.00" />
                                <ValidationMessage For="@(() => course.Cost)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حالة الدورة <span class="text-danger">*</span></label>
                                <InputSelect @bind-Value="course.Status" class="form-select">
                                    <option value="@CourseStatus.Scheduled">مجدولة</option>
                                    <option value="@CourseStatus.InProgress">جارية</option>
                                    <option value="@CourseStatus.Completed">مكتملة</option>
                                    <option value="@CourseStatus.Cancelled">ملغية</option>
                                </InputSelect>
                                <ValidationMessage For="@(() => course.Status)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">ملاحظات</label>
                                <InputTextArea @bind-Value="course.Notes" class="form-control" rows="3" placeholder="أي ملاحظات إضافية حول الدورة" />
                                <ValidationMessage For="@(() => course.Notes)" class="text-danger" />
                            </div>
                        </div>

                        @if (course.StartDate != default && course.EndDate != default && course.EndDate >= course.StartDate)
                        {
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                مدة الدورة: @((course.EndDate - course.StartDate).Days + 1) يوم
                            </div>
                        }

                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                تاريخ الإنشاء: @course.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                @if (course.UpdatedAt.HasValue)
                                {
                                    <br />
                                    <text>آخر تحديث: @course.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</text>
                                }
                            </small>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="/internal-courses" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning" disabled="@isSubmitting">
                                @if (isSubmitting)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>جاري الحفظ...</span>
                                }
                                else
                                {
                                    <i class="fas fa-save me-2"></i>
                                    <span>حفظ التغييرات</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public int Id { get; set; }
    
    private Course? course;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCourse();
    }

    private async Task LoadCourse()
    {
        course = await DbContext.Courses
            .FirstOrDefaultAsync(c => c.Id == Id && c.Type == CourseType.Internal);

        if (course == null)
        {
            Navigation.NavigateTo("/internal-courses");
        }
    }

    private async Task HandleValidSubmit()
    {
        if (course == null) return;

        isSubmitting = true;
        try
        {
            // Validate dates
            if (course.EndDate < course.StartDate)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تاريخ النهاية يجب أن يكون بعد تاريخ البداية.");
                isSubmitting = false;
                return;
            }

            course.UpdatedAt = DateTime.Now;
            DbContext.Courses.Update(course);
            await DbContext.SaveChangesAsync();

            await JSRuntime.InvokeVoidAsync("alert", "تم تحديث بيانات الدورة بنجاح!");
            Navigation.NavigateTo("/internal-courses");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
