body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

.sidebar {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    min-height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    width: 250px;
    z-index: 1000;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.sidebar ::deep .nav-link {
    color: white !important;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 5px 10px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.sidebar ::deep .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

.sidebar ::deep .nav-link:hover {
    background-color: rgba(255,255,255,0.2);
    transform: translateX(-5px);
}

.sidebar ::deep .nav-link.active {
    background-color: rgba(255,255,255,0.3);
}

.main-content {
    margin-right: 250px;
    padding: 0;
    min-height: 100vh;
}

.top-navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    margin-bottom: 20px;
}

.logo-section h4 {
    color: white;
    margin: 0;
    font-weight: 600;
}

.logo-section i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.content-wrapper {
    padding: 20px 30px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

@@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .mobile-toggle {
        display: block !important;
    }
}

.mobile-toggle {
    display: none;
}
