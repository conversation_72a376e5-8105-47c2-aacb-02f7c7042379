using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TraningSys.Models
{
    public enum EducationLevel
    {
        [Display(Name = "بكالوريوس")]
        Bachelor = 1,
        [Display(Name = "ماجستير")]
        Master = 2,
        [Display(Name = "دكتوراه")]
        PhD = 3,
        [Display(Name = "دبلوم عالي")]
        HighDiploma = 4
    }

    public enum RequestStatus
    {
        [Display(Name = "مقدم")]
        Submitted = 1,
        [Display(Name = "قيد المراجعة")]
        UnderReview = 2,
        [Display(Name = "موافق عليه")]
        Approved = 3,
        [Display(Name = "مرفوض")]
        Rejected = 4,
        [Display(Name = "مكتمل")]
        Completed = 5
    }

    public class HigherEducationRequest
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الموظف مطلوب")]
        [Display(Name = "الموظف")]
        public int EmployeeId { get; set; }

        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [Required(ErrorMessage = "مستوى التعليم مطلوب")]
        [Display(Name = "مستوى التعليم")]
        public EducationLevel EducationLevel { get; set; }

        [Required(ErrorMessage = "التخصص مطلوب")]
        [Display(Name = "التخصص")]
        [StringLength(200)]
        public string Specialization { get; set; } = string.Empty;

        [Required(ErrorMessage = "الجامعة مطلوبة")]
        [Display(Name = "الجامعة")]
        [StringLength(200)]
        public string University { get; set; } = string.Empty;

        [Display(Name = "الدولة")]
        [StringLength(100)]
        public string? Country { get; set; }

        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        [Display(Name = "تاريخ البداية")]
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية المتوقع مطلوب")]
        [Display(Name = "تاريخ النهاية المتوقع")]
        [DataType(DataType.Date)]
        public DateTime ExpectedEndDate { get; set; }

        [Display(Name = "التكلفة المقدرة")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? EstimatedCost { get; set; }

        [Required(ErrorMessage = "حالة الطلب مطلوبة")]
        [Display(Name = "حالة الطلب")]
        public RequestStatus Status { get; set; } = RequestStatus.Submitted;

        [Display(Name = "تاريخ الطلب")]
        public DateTime RequestDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ الموافقة")]
        public DateTime? ApprovalDate { get; set; }

        [Display(Name = "سبب الرفض")]
        [StringLength(500)]
        public string? RejectionReason { get; set; }

        [Display(Name = "ملاحظات")]
        [StringLength(1000)]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }
    }
}
