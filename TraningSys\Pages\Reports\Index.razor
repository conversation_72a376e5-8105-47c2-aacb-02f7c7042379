@page "/reports"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<PageTitle>التقارير والإحصائيات</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h2>
    <div>
        <button class="btn btn-success" @onclick="ExportToExcel">
            <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
        </button>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3 class="mb-1">@totalEmployees</h3>
                <p class="mb-0">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                <h3 class="mb-1">@totalCourses</h3>
                <p class="mb-0">إجمالي الدورات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-hand-point-up fa-2x mb-2"></i>
                <h3 class="mb-1">@totalNominations</h3>
                <p class="mb-0">إجمالي الترشيحات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-certificate fa-2x mb-2"></i>
                <h3 class="mb-1">@totalCertificates</h3>
                <p class="mb-0">إجمالي الشهادات</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>فلترة التقارير</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" @bind="fromDate" @onchange="LoadReports" />
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" @bind="toDate" @onchange="LoadReports" />
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع الدورة</label>
                <select class="form-select" @bind="selectedCourseType" @onchange="LoadReports">
                    <option value="">جميع الأنواع</option>
                    <option value="@CourseType.Internal">دورات داخلية</option>
                    <option value="@CourseType.External">دورات خارجية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الإدارة</label>
                <select class="form-select" @bind="selectedDepartmentId" @onchange="LoadReports">
                    <option value="0">جميع الإدارات</option>
                    @if (departments != null)
                    {
                        @foreach (var dept in departments)
                        {
                            <option value="@dept.Id">@dept.Name</option>
                        }
                    }
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Reports Tabs -->
<div class="card shadow-sm">
    <div class="card-header bg-light">
        <ul class="nav nav-tabs card-header-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link @(activeTab == "courses" ? "active" : "")" @onclick="() => SetActiveTab(\"courses\")">
                    <i class="fas fa-chalkboard-teacher me-2"></i>تقرير الدورات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link @(activeTab == "employees" ? "active" : "")" @onclick="() => SetActiveTab(\"employees\")">
                    <i class="fas fa-users me-2"></i>تقرير الموظفين
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link @(activeTab == "departments" ? "active" : "")" @onclick="() => SetActiveTab(\"departments\")">
                    <i class="fas fa-building me-2"></i>تقرير الإدارات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link @(activeTab == "certificates" ? "active" : "")" @onclick="() => SetActiveTab(\"certificates\")">
                    <i class="fas fa-certificate me-2"></i>تقرير الشهادات
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        @if (activeTab == "courses")
        {
            <h6><i class="fas fa-chalkboard-teacher me-2"></i>تقرير الدورات</h6>
            @if (courseReports != null)
            {
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الدورة</th>
                                <th>النوع</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>المدة</th>
                                <th>المرشحين</th>
                                <th>المقبولين</th>
                                <th>المكتملين</th>
                                <th>الشهادات</th>
                                <th>التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var course in courseReports)
                            {
                                <tr>
                                    <td>@course.Name</td>
                                    <td>
                                        @if (course.Type == CourseType.Internal)
                                        {
                                            <span class="badge bg-info">داخلية</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">خارجية</span>
                                        }
                                    </td>
                                    <td>@course.StartDate.ToString("dd/MM/yyyy")</td>
                                    <td>@course.EndDate.ToString("dd/MM/yyyy")</td>
                                    <td>@course.DurationDays يوم</td>
                                    <td>@course.CourseNominations.Count()</td>
                                    <td>@course.CourseNominations.Count(n => n.Status == NominationStatus.Accepted)</td>
                                    <td>@course.CourseNominations.Count(n => n.Status == NominationStatus.Completed)</td>
                                    <td>@course.Certificates.Count()</td>
                                    <td>
                                        @if (course.Cost.HasValue)
                                        {
                                            <span>@course.Cost.Value.ToString("N0") ريال</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        }
        else if (activeTab == "employees")
        {
            <h6><i class="fas fa-users me-2"></i>تقرير الموظفين</h6>
            @if (employeeReports != null)
            {
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الموظف</th>
                                <th>رقم الموظف</th>
                                <th>الإدارة</th>
                                <th>المنصب</th>
                                <th>الترشيحات</th>
                                <th>الدورات المكتملة</th>
                                <th>الشهادات</th>
                                <th>آخر دورة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var employee in employeeReports)
                            {
                                <tr>
                                    <td>@employee.FullName</td>
                                    <td>@employee.EmployeeNumber</td>
                                    <td>@employee.Department?.Name</td>
                                    <td>@employee.Position?.Name</td>
                                    <td>@employee.CourseNominations.Count()</td>
                                    <td>@employee.CourseNominations.Count(n => n.Status == NominationStatus.Completed)</td>
                                    <td>@employee.Certificates.Count()</td>
                                    <td>
                                        @{
                                            var lastCourse = employee.CourseNominations
                                                .Where(n => n.Status == NominationStatus.Completed)
                                                .OrderByDescending(n => n.Course.EndDate)
                                                .FirstOrDefault();
                                        }
                                        @if (lastCourse != null)
                                        {
                                            <span>@lastCourse.Course.Name</span><br />
                                            <small class="text-muted">@lastCourse.Course.EndDate.ToString("dd/MM/yyyy")</small>
                                        }
                                        else
                                        {
                                            <span class="text-muted">لا يوجد</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        }
        else if (activeTab == "departments")
        {
            <h6><i class="fas fa-building me-2"></i>تقرير الإدارات</h6>
            @if (departmentReports != null)
            {
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الإدارة</th>
                                <th>عدد الموظفين</th>
                                <th>الموظفين النشطين</th>
                                <th>إجمالي الترشيحات</th>
                                <th>الدورات المكتملة</th>
                                <th>الشهادات المصدرة</th>
                                <th>معدل المشاركة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var dept in departmentReports)
                            {
                                <tr>
                                    <td>@dept.Name</td>
                                    <td>@dept.Employees.Count()</td>
                                    <td>@dept.Employees.Count(e => e.IsActive)</td>
                                    <td>@dept.Employees.SelectMany(e => e.CourseNominations).Count()</td>
                                    <td>@dept.Employees.SelectMany(e => e.CourseNominations).Count(n => n.Status == NominationStatus.Completed)</td>
                                    <td>@dept.Employees.SelectMany(e => e.Certificates).Count()</td>
                                    <td>
                                        @{
                                            var activeEmployees = dept.Employees.Count(e => e.IsActive);
                                            var participatingEmployees = dept.Employees.Count(e => e.CourseNominations.Any());
                                            var participationRate = activeEmployees > 0 ? (participatingEmployees * 100.0 / activeEmployees) : 0;
                                        }
                                        @participationRate.ToString("F1")%
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        }
        else if (activeTab == "certificates")
        {
            <h6><i class="fas fa-certificate me-2"></i>تقرير الشهادات</h6>
            @if (certificateReports != null)
            {
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الشهادة</th>
                                <th>الموظف</th>
                                <th>الدورة</th>
                                <th>نوع الدورة</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var cert in certificateReports)
                            {
                                <tr>
                                    <td>@cert.CertificateNumber</td>
                                    <td>@cert.Employee.FullName</td>
                                    <td>@cert.Course.Name</td>
                                    <td>
                                        @if (cert.Course.Type == CourseType.Internal)
                                        {
                                            <span class="badge bg-info">داخلية</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">خارجية</span>
                                        }
                                    </td>
                                    <td>@cert.IssueDate.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        @if (cert.ExpiryDate.HasValue)
                                        {
                                            <span>@cert.ExpiryDate.Value.ToString("dd/MM/yyyy")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">لا تنتهي</span>
                                        }
                                    </td>
                                    <td>
                                        @if (cert.IsActive)
                                        {
                                            @if (cert.ExpiryDate.HasValue && cert.ExpiryDate.Value < DateTime.Today)
                                            {
                                                <span class="badge bg-danger">منتهية</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">نشطة</span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">ملغية</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        }
    </div>
</div>

@code {
    // Statistics
    private int totalEmployees = 0;
    private int totalCourses = 0;
    private int totalNominations = 0;
    private int totalCertificates = 0;

    // Filters
    private DateTime? fromDate = DateTime.Today.AddMonths(-6);
    private DateTime? toDate = DateTime.Today;
    private string selectedCourseType = "";
    private int selectedDepartmentId = 0;

    // Data
    private List<Department>? departments;
    private List<Course>? courseReports;
    private List<Employee>? employeeReports;
    private List<Department>? departmentReports;
    private List<Certificate>? certificateReports;

    // UI State
    private string activeTab = "courses";

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
        await LoadDepartments();
        await LoadReports();
    }

    private async Task LoadStatistics()
    {
        totalEmployees = await DbContext.Employees.CountAsync(e => e.IsActive);
        totalCourses = await DbContext.Courses.CountAsync();
        totalNominations = await DbContext.CourseNominations.CountAsync();
        totalCertificates = await DbContext.Certificates.CountAsync();
    }

    private async Task LoadDepartments()
    {
        departments = await DbContext.Departments
            .OrderBy(d => d.Name)
            .ToListAsync();
    }

    private async Task LoadReports()
    {
        await LoadCourseReports();
        await LoadEmployeeReports();
        await LoadDepartmentReports();
        await LoadCertificateReports();
    }

    private async Task LoadCourseReports()
    {
        var query = DbContext.Courses
            .Include(c => c.CourseNominations)
            .Include(c => c.Certificates)
            .AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(c => c.StartDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(c => c.StartDate <= toDate.Value);

        if (!string.IsNullOrWhiteSpace(selectedCourseType) && Enum.TryParse<CourseType>(selectedCourseType, out var courseType))
            query = query.Where(c => c.Type == courseType);

        courseReports = await query
            .OrderByDescending(c => c.StartDate)
            .ToListAsync();
    }

    private async Task LoadEmployeeReports()
    {
        var query = DbContext.Employees
            .Include(e => e.Department)
            .Include(e => e.Position)
            .Include(e => e.CourseNominations)
            .ThenInclude(n => n.Course)
            .Include(e => e.Certificates)
            .Where(e => e.IsActive)
            .AsQueryable();

        if (selectedDepartmentId > 0)
            query = query.Where(e => e.DepartmentId == selectedDepartmentId);

        employeeReports = await query
            .OrderBy(e => e.FirstName)
            .ThenBy(e => e.LastName)
            .ToListAsync();
    }

    private async Task LoadDepartmentReports()
    {
        departmentReports = await DbContext.Departments
            .Include(d => d.Employees)
            .ThenInclude(e => e.CourseNominations)
            .Include(d => d.Employees)
            .ThenInclude(e => e.Certificates)
            .OrderBy(d => d.Name)
            .ToListAsync();
    }

    private async Task LoadCertificateReports()
    {
        var query = DbContext.Certificates
            .Include(c => c.Employee)
            .Include(c => c.Course)
            .AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(c => c.IssueDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(c => c.IssueDate <= toDate.Value);

        if (!string.IsNullOrWhiteSpace(selectedCourseType) && Enum.TryParse<CourseType>(selectedCourseType, out var courseType))
            query = query.Where(c => c.Course.Type == courseType);

        if (selectedDepartmentId > 0)
            query = query.Where(c => c.Employee.DepartmentId == selectedDepartmentId);

        certificateReports = await query
            .OrderByDescending(c => c.IssueDate)
            .ToListAsync();
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
    }

    private async Task ExportToExcel()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة التصدير إلى Excel ستكون متاحة قريباً");
    }
}
