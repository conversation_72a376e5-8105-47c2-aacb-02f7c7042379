@page "/employees/details/{Id:int}"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation

<PageTitle>تفاصيل الموظف</PageTitle>

@if (employee == null)
{
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات الموظف...</p>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user me-2"></i>تفاصيل الموظف</h2>
        <div>
            <a href="/employees/edit/@Id" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>تعديل
            </a>
            <a href="/employees" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- Employee Profile Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <div class="avatar-lg bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        @employee.FirstName.Substring(0, 1)@employee.LastName.Substring(0, 1)
                    </div>
                    <h4 class="mb-1">@employee.FullName</h4>
                    <p class="text-muted mb-2">@employee.Position?.Name</p>
                    <p class="text-muted">@employee.Department?.Name</p>
                    
                    @if (employee.IsActive)
                    {
                        <span class="badge bg-success fs-6">موظف نشط</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary fs-6">غير نشط</span>
                    }
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-address-book me-2"></i>معلومات الاتصال</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">البريد الإلكتروني</label>
                        <div>
                            <a href="mailto:@employee.Email" class="text-decoration-none">
                                <i class="fas fa-envelope me-2"></i>@employee.Email
                            </a>
                        </div>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(employee.PhoneNumber))
                    {
                        <div class="mb-3">
                            <label class="form-label text-muted">رقم الهاتف</label>
                            <div>
                                <a href="tel:@employee.PhoneNumber" class="text-decoration-none">
                                    <i class="fas fa-phone me-2"></i>@employee.PhoneNumber
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">رقم الموظف</label>
                            <div class="fw-bold">@employee.EmployeeNumber</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الاسم الكامل</label>
                            <div class="fw-bold">@employee.FullName</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الإدارة</label>
                            <div class="fw-bold">@employee.Department?.Name</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">المنصب</label>
                            <div class="fw-bold">@employee.Position?.Name</div>
                        </div>
                        @if (employee.HireDate.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ التوظيف</label>
                                <div class="fw-bold">@employee.HireDate.Value.ToString("dd/MM/yyyy")</div>
                            </div>
                        }
                        @if (employee.Salary.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الراتب</label>
                                <div class="fw-bold">@employee.Salary.Value.ToString("N2") ريال</div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Training History -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>تاريخ التدريب</h6>
                    <a href="/nominations/create?employeeId=@employee.Id" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>ترشيح لدورة
                    </a>
                </div>
                <div class="card-body">
                    @if (courseNominations == null)
                    {
                        <div class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                            <span class="ms-2">جاري تحميل تاريخ التدريب...</span>
                        </div>
                    }
                    else if (!courseNominations.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-graduation-cap fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد دورات تدريبية</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>اسم الدورة</th>
                                        <th>النوع</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var nomination in courseNominations)
                                    {
                                        <tr>
                                            <td>@nomination.Course.Name</td>
                                            <td>
                                                @if (nomination.Course.Type == CourseType.Internal)
                                                {
                                                    <span class="badge bg-info">داخلية</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">خارجية</span>
                                                }
                                            </td>
                                            <td>@nomination.Course.StartDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @switch (nomination.Status)
                                                {
                                                    case NominationStatus.Nominated:
                                                        <span class="badge bg-primary">مرشح</span>
                                                        break;
                                                    case NominationStatus.Accepted:
                                                        <span class="badge bg-success">مقبول</span>
                                                        break;
                                                    case NominationStatus.Rejected:
                                                        <span class="badge bg-danger">مرفوض</span>
                                                        break;
                                                    case NominationStatus.Excused:
                                                        <span class="badge bg-warning">معتذر</span>
                                                        break;
                                                    case NominationStatus.Completed:
                                                        <span class="badge bg-success">مكتمل</span>
                                                        break;
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>

            <!-- System Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-cog me-2"></i>معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ الإنشاء</label>
                            <div>@employee.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                        @if (employee.UpdatedAt.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">آخر تحديث</label>
                                <div>@employee.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public int Id { get; set; }
    
    private Employee? employee;
    private List<CourseNomination>? courseNominations;

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployee();
        if (employee != null)
        {
            await LoadCourseNominations();
        }
    }

    private async Task LoadEmployee()
    {
        employee = await DbContext.Employees
            .Include(e => e.Department)
            .Include(e => e.Position)
            .FirstOrDefaultAsync(e => e.Id == Id);

        if (employee == null)
        {
            Navigation.NavigateTo("/employees");
        }
    }

    private async Task LoadCourseNominations()
    {
        courseNominations = await DbContext.CourseNominations
            .Include(cn => cn.Course)
            .Where(cn => cn.EmployeeId == Id)
            .OrderByDescending(cn => cn.Course.StartDate)
            .Take(10)
            .ToListAsync();
    }
}
