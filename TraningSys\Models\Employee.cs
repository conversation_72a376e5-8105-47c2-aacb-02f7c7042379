using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TraningSys.Models
{
    public class Employee
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "رقم الموظف مطلوب")]
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأوسط مطلوب")]
        [Display(Name = "الاسم الأوسط")]
        [StringLength(50)]
        public string MiddleName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [Display(Name = "الاسم الأخير")]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Display(Name = "الاسم الكامل")]
        public string FullName => $"{FirstName} {MiddleName} {LastName}";

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [Display(Name = "رقم الهاتف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? PhoneNumber { get; set; }

        [Required(ErrorMessage = "الإدارة مطلوبة")]
        [Display(Name = "الإدارة")]
        public int DepartmentId { get; set; }

        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; } = null!;

        [Required(ErrorMessage = "المنصب مطلوب")]
        [Display(Name = "المنصب")]
        public int PositionId { get; set; }

        [ForeignKey("PositionId")]
        public virtual Position Position { get; set; } = null!;

        [Display(Name = "تاريخ التوظيف")]
        [DataType(DataType.Date)]
        public DateTime? HireDate { get; set; }

        [Display(Name = "الراتب")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Salary { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<CourseNomination> CourseNominations { get; set; } = new List<CourseNomination>();
        public virtual ICollection<HigherEducationRequest> HigherEducationRequests { get; set; } = new List<HigherEducationRequest>();
        public virtual ICollection<Certificate> Certificates { get; set; } = new List<Certificate>();
    }
}
