@page "/higher-education/create"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>إضافة طلب دراسات عليا جديد</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus me-2"></i>إضافة طلب دراسات عليا جديد</h2>
    <a href="/higher-education" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>بيانات طلب الدراسات العليا</h5>
            </div>
            <div class="card-body">
                <EditForm Model="request" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">الموظف <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="request.EmployeeId" class="form-select">
                                <option value="0">-- اختر الموظف --</option>
                                @if (employees != null)
                                {
                                    @foreach (var employee in employees)
                                    {
                                        <option value="@employee.Id">@employee.FullName - @employee.EmployeeNumber</option>
                                    }
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => request.EmployeeId)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مستوى التعليم <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="request.EducationLevel" class="form-select">
                                <option value="@EducationLevel.Bachelor">بكالوريوس</option>
                                <option value="@EducationLevel.HighDiploma">دبلوم عالي</option>
                                <option value="@EducationLevel.Master">ماجستير</option>
                                <option value="@EducationLevel.PhD">دكتوراه</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => request.EducationLevel)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">التخصص <span class="text-danger">*</span></label>
                            <InputText @bind-Value="request.Specialization" class="form-control" placeholder="أدخل التخصص المطلوب" />
                            <ValidationMessage For="@(() => request.Specialization)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الجامعة <span class="text-danger">*</span></label>
                            <InputText @bind-Value="request.University" class="form-control" placeholder="اسم الجامعة" />
                            <ValidationMessage For="@(() => request.University)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الدولة</label>
                            <InputText @bind-Value="request.Country" class="form-control" placeholder="دولة الدراسة" />
                            <ValidationMessage For="@(() => request.Country)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية المتوقع <span class="text-danger">*</span></label>
                            <InputDate @bind-Value="request.StartDate" class="form-control" />
                            <ValidationMessage For="@(() => request.StartDate)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ النهاية المتوقع</label>
                            <InputDate @bind-Value="request.EndDate" class="form-control" />
                            <ValidationMessage For="@(() => request.EndDate)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">التكلفة المقدرة (ريال)</label>
                            <InputNumber @bind-Value="request.EstimatedCost" class="form-control" placeholder="0.00" />
                            <ValidationMessage For="@(() => request.EstimatedCost)" class="text-danger" />
                            <div class="form-text">التكلفة الإجمالية المقدرة للدراسة</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة الطلب <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="request.Status" class="form-select">
                                <option value="@RequestStatus.Submitted">مقدم</option>
                                <option value="@RequestStatus.UnderReview">قيد المراجعة</option>
                                <option value="@RequestStatus.Approved">موافق عليه</option>
                                <option value="@RequestStatus.Rejected">مرفوض</option>
                                <option value="@RequestStatus.Completed">مكتمل</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => request.Status)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">وصف البرنامج</label>
                            <InputTextArea @bind-Value="request.ProgramDescription" class="form-control" rows="3" placeholder="وصف مختصر عن البرنامج الدراسي وأهدافه" />
                            <ValidationMessage For="@(() => request.ProgramDescription)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">ملاحظات</label>
                            <InputTextArea @bind-Value="request.Notes" class="form-control" rows="3" placeholder="أي ملاحظات إضافية حول الطلب" />
                            <ValidationMessage For="@(() => request.Notes)" class="text-danger" />
                        </div>
                    </div>

                    @if (request.StartDate != default && request.EndDate.HasValue && request.EndDate >= request.StartDate)
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            مدة الدراسة المقدرة: @((request.EndDate.Value - request.StartDate).Days / 365.0:F1) سنة
                        </div>
                    }

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> طلبات الدراسات العليا تتطلب موافقات إدارية متعددة وقد تستغرق وقتاً طويلاً للمعالجة.
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="/higher-education" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save me-2"></i>
                                <span>حفظ الطلب</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private HigherEducationRequest request = new HigherEducationRequest 
    { 
        Status = RequestStatus.Submitted,
        StartDate = DateTime.Today.AddMonths(6),
        RequestDate = DateTime.Now
    };
    private List<Employee>? employees;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployees();
    }

    private async Task LoadEmployees()
    {
        employees = await DbContext.Employees
            .Where(e => e.IsActive)
            .OrderBy(e => e.FirstName)
            .ThenBy(e => e.LastName)
            .ToListAsync();
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            // Validate employee selection
            if (request.EmployeeId == 0)
            {
                await JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار الموظف.");
                isSubmitting = false;
                return;
            }

            // Validate dates
            if (request.EndDate.HasValue && request.EndDate < request.StartDate)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تاريخ النهاية يجب أن يكون بعد تاريخ البداية.");
                isSubmitting = false;
                return;
            }

            // Check if employee already has an active request
            var existingRequest = await DbContext.HigherEducationRequests
                .FirstOrDefaultAsync(r => r.EmployeeId == request.EmployeeId && 
                                         (r.Status == RequestStatus.Submitted || 
                                          r.Status == RequestStatus.UnderReview || 
                                          r.Status == RequestStatus.Approved));

            if (existingRequest != null)
            {
                await JSRuntime.InvokeVoidAsync("alert", "يوجد طلب دراسات عليا نشط للموظف المحدد.");
                isSubmitting = false;
                return;
            }

            request.RequestDate = DateTime.Now;
            DbContext.HigherEducationRequests.Add(request);
            await DbContext.SaveChangesAsync();

            await JSRuntime.InvokeVoidAsync("alert", "تم إضافة طلب الدراسات العليا بنجاح!");
            Navigation.NavigateTo("/higher-education");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
