@page "/external-trainees"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>المتدربين الخارجيين</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-friends me-2"></i>المتدربين الخارجيين</h2>
    <a href="/external-trainees/create" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>إضافة متدرب جديد
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">قائمة المتدربين الخارجيين</h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في المتدربين..." 
                           @bind="searchTerm" @onkeyup="SearchTrainees" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="SearchTrainees">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        @if (trainees == null)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>
        }
        else if (!trainees.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد متدربين خارجيين</h5>
                <p class="text-muted">ابدأ بإضافة متدرب جديد</p>
                <a href="/external-trainees/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة متدرب
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>اسم المتدرب</th>
                            <th>الجهة</th>
                            <th>المنصب</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var trainee in trainees)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-secondary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                            @trainee.FirstName.Substring(0, 1)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@trainee.FullName</div>
                                            @if (!string.IsNullOrEmpty(trainee.NationalId))
                                            {
                                                <small class="text-muted">@trainee.NationalId</small>
                                            }
                                        </div>
                                    </div>
                                </td>
                                <td>@trainee.Organization</td>
                                <td>@trainee.Position</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(trainee.Email))
                                    {
                                        <a href="mailto:@trainee.Email" class="text-decoration-none">@trainee.Email</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">غير محدد</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(trainee.Phone))
                                    {
                                        <a href="tel:@trainee.Phone" class="text-decoration-none">@trainee.Phone</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">غير محدد</span>
                                    }
                                </td>
                                <td>@trainee.RegistrationDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    @if (trainee.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">غير نشط</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/external-trainees/details/@trainee.Id" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/external-trainees/edit/@trainee.Id" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" title="حذف" 
                                                @onclick="() => ConfirmDelete(trainee.Id, trainee.FullName)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض @trainees.Count() من أصل @totalCount متدرب
                    </small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage - 1)">السابق</button>
                        </li>
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => LoadPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage + 1)">التالي</button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    </div>
</div>

@code {
    private List<ExternalTrainee>? trainees;
    private string searchTerm = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadTrainees();
    }

    private async Task LoadTrainees()
    {
        var query = DbContext.ExternalTrainees.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(t => 
                t.FirstName.Contains(searchTerm) ||
                t.LastName.Contains(searchTerm) ||
                (t.NationalId != null && t.NationalId.Contains(searchTerm)) ||
                (t.Email != null && t.Email.Contains(searchTerm)) ||
                (t.Organization != null && t.Organization.Contains(searchTerm)) ||
                (t.Position != null && t.Position.Contains(searchTerm)));
        }

        totalCount = await query.CountAsync();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        trainees = await query
            .OrderByDescending(t => t.RegistrationDate)
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private async Task SearchTrainees()
    {
        currentPage = 1;
        await LoadTrainees();
    }

    private async Task LoadPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadTrainees();
        }
    }

    private async Task ConfirmDelete(int traineeId, string traineeName)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"هل أنت متأكد من حذف المتدرب '{traineeName}'؟\nهذا الإجراء لا يمكن التراجع عنه.");
        
        if (confirmed)
        {
            await DeleteTrainee(traineeId);
        }
    }

    private async Task DeleteTrainee(int traineeId)
    {
        try
        {
            var trainee = await DbContext.ExternalTrainees.FindAsync(traineeId);
            if (trainee != null)
            {
                DbContext.ExternalTrainees.Remove(trainee);
                await DbContext.SaveChangesAsync();
                await LoadTrainees();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المتدرب بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حذف المتدرب: {ex.Message}");
        }
    }
}
