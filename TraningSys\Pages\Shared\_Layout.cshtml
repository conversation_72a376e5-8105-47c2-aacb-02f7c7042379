@using Microsoft.AspNetCore.Components.Web
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />
    <title>@ViewData["Title"] - نظام التدريب</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <link href="css/site.css" rel="stylesheet" />
    <link href="TraningSys.styles.css" rel="stylesheet" />
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            min-height: 100vh;
            position: fixed;
            right: 0;
            top: 0;
            width: 250px;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            color: white !important;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.3);
        }
        
        .main-content {
            margin-right: 250px;
            padding: 0;
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 30px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-section {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }
        
        .logo-section h4 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .logo-section i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .content-wrapper {
            padding: 20px 30px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        @@media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .mobile-toggle {
                display: block !important;
            }
        }
        
        .mobile-toggle {
            display: none;
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="logo-section">
                <i class="fas fa-graduation-cap"></i>
                <h4>نظام التدريب</h4>
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="/">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/employees">
                        <i class="fas fa-users"></i>
                        الموظفين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/internal-courses">
                        <i class="fas fa-chalkboard-teacher"></i>
                        الدورات الداخلية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/external-courses">
                        <i class="fas fa-university"></i>
                        الدورات الخارجية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/higher-education">
                        <i class="fas fa-user-graduate"></i>
                        الدراسات العليا
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/external-trainees">
                        <i class="fas fa-user-plus"></i>
                        المتدربين الخارجيين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/nominations">
                        <i class="fas fa-hand-point-up"></i>
                        الترشيح
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/certificates">
                        <i class="fas fa-certificate"></i>
                        الشهادات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="fas fa-chart-bar"></i>
                        التقارير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navbar -->
            <nav class="top-navbar">
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary mobile-toggle me-3" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h5 class="mb-0">@ViewData["Title"]</h5>
                </div>
                
                <div class="user-info">
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <div class="user-avatar">
                            @User.Identity.Name?.Substring(0, 1).ToUpper()
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link dropdown-toggle text-decoration-none" type="button" data-bs-toggle="dropdown">
                                @User.Identity.Name
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="/Account/Manage"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-area="Identity" asp-page="/Account/Logout" method="post" class="d-inline">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    }
                    else
                    {
                        <a href="/Identity/Account/Login" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    }
                </div>
            </nav>

            <!-- Content -->
            <div class="content-wrapper">
                @RenderBody()
            </div>
        </div>
    </div>

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            حدث خطأ. قد لا يستجيب التطبيق حتى يتم إعادة تحميله.
        </environment>
        <environment include="Development">
            حدث استثناء غير معالج. راجع أدوات المطور في المتصفح للحصول على التفاصيل.
        </environment>
        <a href="" class="reload">إعادة تحميل</a>
        <a class="dismiss">🗙</a>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="_framework/blazor.server.js"></script>
    
    <script>
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
