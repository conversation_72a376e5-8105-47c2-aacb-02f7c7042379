@page "/higher-education"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>الدراسات العليا</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-graduate me-2"></i>طلبات الدراسات العليا</h2>
    <a href="/higher-education/create" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>إضافة طلب جديد
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">قائمة طلبات الدراسات العليا</h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في الطلبات..." 
                           @bind="searchTerm" @onkeyup="SearchRequests" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="SearchRequests">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        @if (requests == null)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>
        }
        else if (!requests.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات دراسات عليا</h5>
                <p class="text-muted">ابدأ بإضافة طلب جديد</p>
                <a href="/higher-education/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة طلب
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الموظف</th>
                            <th>مستوى التعليم</th>
                            <th>التخصص</th>
                            <th>الجامعة</th>
                            <th>تاريخ البداية</th>
                            <th>التكلفة المقدرة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var request in requests)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-info text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                            @request.Employee.FirstName.Substring(0, 1)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@request.Employee.FullName</div>
                                            <small class="text-muted">@request.Employee.EmployeeNumber</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @switch (request.EducationLevel)
                                    {
                                        case EducationLevel.Bachelor:
                                            <span class="badge bg-primary">بكالوريوس</span>
                                            break;
                                        case EducationLevel.Master:
                                            <span class="badge bg-success">ماجستير</span>
                                            break;
                                        case EducationLevel.PhD:
                                            <span class="badge bg-warning">دكتوراه</span>
                                            break;
                                        case EducationLevel.HighDiploma:
                                            <span class="badge bg-info">دبلوم عالي</span>
                                            break;
                                    }
                                </td>
                                <td>@request.Specialization</td>
                                <td>
                                    <div>
                                        <div class="fw-bold">@request.University</div>
                                        @if (!string.IsNullOrEmpty(request.Country))
                                        {
                                            <small class="text-muted">@request.Country</small>
                                        }
                                    </div>
                                </td>
                                <td>@request.StartDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    @if (request.EstimatedCost.HasValue)
                                    {
                                        <span class="fw-bold text-success">@request.EstimatedCost.Value.ToString("N0") ريال</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">غير محدد</span>
                                    }
                                </td>
                                <td>
                                    @switch (request.Status)
                                    {
                                        case RequestStatus.Submitted:
                                            <span class="badge bg-primary">مقدم</span>
                                            break;
                                        case RequestStatus.UnderReview:
                                            <span class="badge bg-warning">قيد المراجعة</span>
                                            break;
                                        case RequestStatus.Approved:
                                            <span class="badge bg-success">موافق عليه</span>
                                            break;
                                        case RequestStatus.Rejected:
                                            <span class="badge bg-danger">مرفوض</span>
                                            break;
                                        case RequestStatus.Completed:
                                            <span class="badge bg-info">مكتمل</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/higher-education/details/@request.Id" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/higher-education/edit/@request.Id" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" title="حذف" 
                                                @onclick="() => ConfirmDelete(request.Id, request.Employee.FullName)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض @requests.Count() من أصل @totalCount طلب
                    </small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage - 1)">السابق</button>
                        </li>
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => LoadPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage + 1)">التالي</button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    </div>
</div>

@code {
    private List<HigherEducationRequest>? requests;
    private string searchTerm = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadRequests();
    }

    private async Task LoadRequests()
    {
        var query = DbContext.HigherEducationRequests
            .Include(r => r.Employee)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(r => 
                r.Employee.FirstName.Contains(searchTerm) ||
                r.Employee.LastName.Contains(searchTerm) ||
                r.Employee.EmployeeNumber.Contains(searchTerm) ||
                r.Specialization.Contains(searchTerm) ||
                r.University.Contains(searchTerm));
        }

        totalCount = await query.CountAsync();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        requests = await query
            .OrderByDescending(r => r.RequestDate)
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private async Task SearchRequests()
    {
        currentPage = 1;
        await LoadRequests();
    }

    private async Task LoadPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadRequests();
        }
    }

    private async Task ConfirmDelete(int requestId, string employeeName)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"هل أنت متأكد من حذف طلب الدراسات العليا للموظف '{employeeName}'؟\nهذا الإجراء لا يمكن التراجع عنه.");
        
        if (confirmed)
        {
            await DeleteRequest(requestId);
        }
    }

    private async Task DeleteRequest(int requestId)
    {
        try
        {
            var request = await DbContext.HigherEducationRequests.FindAsync(requestId);
            if (request != null)
            {
                DbContext.HigherEducationRequests.Remove(request);
                await DbContext.SaveChangesAsync();
                await LoadRequests();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الطلب بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حذف الطلب: {ex.Message}");
        }
    }
}
