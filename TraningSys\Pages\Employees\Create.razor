@page "/employees/create"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>إضافة موظف جديد</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-2"></i>إضافة موظف جديد</h2>
    <a href="/employees" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>بيانات الموظف</h5>
            </div>
            <div class="card-body">
                <EditForm Model="employee" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الموظف <span class="text-danger">*</span></label>
                            <InputText @bind-Value="employee.EmployeeNumber" class="form-control" placeholder="أدخل رقم الموظف" />
                            <ValidationMessage For="@(() => employee.EmployeeNumber)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <InputText @bind-Value="employee.Email" class="form-control" placeholder="<EMAIL>" />
                            <ValidationMessage For="@(() => employee.Email)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                            <InputText @bind-Value="employee.FirstName" class="form-control" placeholder="الاسم الأول" />
                            <ValidationMessage For="@(() => employee.FirstName)" class="text-danger" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الاسم الأوسط <span class="text-danger">*</span></label>
                            <InputText @bind-Value="employee.MiddleName" class="form-control" placeholder="الاسم الأوسط" />
                            <ValidationMessage For="@(() => employee.MiddleName)" class="text-danger" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                            <InputText @bind-Value="employee.LastName" class="form-control" placeholder="الاسم الأخير" />
                            <ValidationMessage For="@(() => employee.LastName)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <InputText @bind-Value="employee.PhoneNumber" class="form-control" placeholder="05xxxxxxxx" />
                            <ValidationMessage For="@(() => employee.PhoneNumber)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ التوظيف</label>
                            <InputDate @bind-Value="employee.HireDate" class="form-control" />
                            <ValidationMessage For="@(() => employee.HireDate)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الإدارة <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="employee.DepartmentId" class="form-select">
                                <option value="0">-- اختر الإدارة --</option>
                                @if (departments != null)
                                {
                                    @foreach (var dept in departments)
                                    {
                                        <option value="@dept.Id">@dept.Name</option>
                                    }
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => employee.DepartmentId)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المنصب <span class="text-danger">*</span></label>
                            <InputSelect @bind-Value="employee.PositionId" class="form-select">
                                <option value="0">-- اختر المنصب --</option>
                                @if (positions != null)
                                {
                                    @foreach (var pos in positions)
                                    {
                                        <option value="@pos.Id">@pos.Name</option>
                                    }
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => employee.PositionId)" class="text-danger" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الراتب</label>
                            <InputNumber @bind-Value="employee.Salary" class="form-control" placeholder="0.00" />
                            <ValidationMessage For="@(() => employee.Salary)" class="text-danger" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <InputCheckbox @bind-Value="employee.IsActive" class="form-check-input" id="isActive" />
                                <label class="form-check-label" for="isActive">
                                    موظف نشط
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="/employees" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save me-2"></i>
                                <span>حفظ الموظف</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private Employee employee = new Employee { IsActive = true };
    private List<Department>? departments;
    private List<Position>? positions;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadLookupData();
    }

    private async Task LoadLookupData()
    {
        departments = await DbContext.Departments
            .Where(d => d.IsActive)
            .OrderBy(d => d.Name)
            .ToListAsync();

        positions = await DbContext.Positions
            .Where(p => p.IsActive)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            // Check if employee number already exists
            var existingEmployee = await DbContext.Employees
                .FirstOrDefaultAsync(e => e.EmployeeNumber == employee.EmployeeNumber);
            
            if (existingEmployee != null)
            {
                await JSRuntime.InvokeVoidAsync("alert", "رقم الموظف موجود مسبقاً. يرجى استخدام رقم آخر.");
                isSubmitting = false;
                return;
            }

            // Check if email already exists
            var existingEmail = await DbContext.Employees
                .FirstOrDefaultAsync(e => e.Email == employee.Email);
            
            if (existingEmail != null)
            {
                await JSRuntime.InvokeVoidAsync("alert", "البريد الإلكتروني موجود مسبقاً. يرجى استخدام بريد آخر.");
                isSubmitting = false;
                return;
            }

            employee.CreatedAt = DateTime.Now;
            DbContext.Employees.Add(employee);
            await DbContext.SaveChangesAsync();

            await JSRuntime.InvokeVoidAsync("alert", "تم إضافة الموظف بنجاح!");
            Navigation.NavigateTo("/employees");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
