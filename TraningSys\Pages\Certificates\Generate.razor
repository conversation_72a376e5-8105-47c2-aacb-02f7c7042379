@page "/certificates/generate"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>إصدار شهادات جديدة</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-certificate me-2"></i>إصدار شهادات جديدة</h2>
    <a href="/certificates" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-certificate me-2"></i>إصدار شهادات للمتدربين المكملين</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label class="form-label">اختر الدورة <span class="text-danger">*</span></label>
                        <select class="form-select" @bind="selectedCourseId" @onchange="OnCourseChanged">
                            <option value="0">-- اختر الدورة --</option>
                            @if (completedCourses != null)
                            {
                                @foreach (var course in completedCourses)
                                {
                                    <option value="@course.Id">
                                        @course.Name - @course.StartDate.ToString("dd/MM/yyyy") 
                                        (@(course.Type == CourseType.Internal ? "داخلية" : "خارجية"))
                                    </option>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">تاريخ انتهاء الشهادة</label>
                        <input type="date" class="form-control" @bind="expiryDate" />
                        <div class="form-text">اتركه فارغاً إذا كانت الشهادة لا تنتهي</div>
                    </div>
                </div>

                @if (selectedCourse != null)
                {
                    <div class="alert alert-info mb-4">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الدورة المختارة:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>اسم الدورة:</strong> @selectedCourse.Name<br />
                                <strong>تاريخ البداية:</strong> @selectedCourse.StartDate.ToString("dd/MM/yyyy")<br />
                                <strong>تاريخ النهاية:</strong> @selectedCourse.EndDate.ToString("dd/MM/yyyy")
                            </div>
                            <div class="col-md-6">
                                <strong>نوع الدورة:</strong> @(selectedCourse.Type == CourseType.Internal ? "داخلية" : "خارجية")<br />
                                <strong>المدة:</strong> @selectedCourse.DurationDays يوم<br />
                                <strong>الحالة:</strong> <span class="badge bg-success">مكتملة</span>
                            </div>
                        </div>
                    </div>

                    @if (eligibleNominations != null)
                    {
                        <div class="card">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">المتدربين المؤهلين لإصدار الشهادات (@eligibleNominations.Count)</h6>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-2" @onclick="SelectAll">
                                            <i class="fas fa-check-square me-1"></i>تحديد الكل
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" @onclick="ClearAll">
                                            <i class="fas fa-square me-1"></i>إلغاء التحديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                @if (!eligibleNominations.Any())
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-exclamation-circle fa-2x text-warning mb-2"></i>
                                        <h6>لا يوجد متدربين مؤهلين</h6>
                                        <p class="text-muted">لا يوجد متدربين مكملين لهذه الدورة أو تم إصدار شهادات لهم مسبقاً</p>
                                    </div>
                                }
                                else
                                {
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th width="50">
                                                        <input type="checkbox" class="form-check-input" 
                                                               @onchange="@((e) => ToggleAll((bool)e.Value!))" />
                                                    </th>
                                                    <th>الموظف</th>
                                                    <th>الإدارة</th>
                                                    <th>تاريخ الترشيح</th>
                                                    <th>حالة الترشيح</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var nomination in eligibleNominations)
                                                {
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" class="form-check-input" 
                                                                   @onchange="@((e) => ToggleNomination(nomination.Id, (bool)e.Value!))"
                                                                   checked="@selectedNominationIds.Contains(nomination.Id)" />
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="avatar-sm bg-success text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                                    @nomination.Employee.FirstName.Substring(0, 1)
                                                                </div>
                                                                <div>
                                                                    <div class="fw-bold">@nomination.Employee.FullName</div>
                                                                    <small class="text-muted">@nomination.Employee.EmployeeNumber</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>@nomination.Employee.Department?.Name</td>
                                                        <td>@nomination.NominationDate.ToString("dd/MM/yyyy")</td>
                                                        <td>
                                                            <span class="badge bg-info">مكتمل</span>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="mt-3">
                                        <div class="alert alert-success">
                                            <i class="fas fa-info-circle me-2"></i>
                                            تم تحديد <strong>@selectedNominationIds.Count</strong> من أصل <strong>@eligibleNominations.Count</strong> متدرب لإصدار الشهادات
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                }

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <a href="/certificates" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button class="btn btn-success" @onclick="GenerateCertificates" 
                            disabled="@(isGenerating || selectedNominationIds.Count == 0 || selectedCourseId == 0)">
                        @if (isGenerating)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            <span>جاري الإصدار...</span>
                        }
                        else
                        {
                            <i class="fas fa-certificate me-2"></i>
                            <span>إصدار @selectedNominationIds.Count شهادة</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<Course>? completedCourses;
    private List<CourseNomination>? eligibleNominations;
    private Course? selectedCourse;
    private int selectedCourseId = 0;
    private DateTime? expiryDate;
    private HashSet<int> selectedNominationIds = new();
    private bool isGenerating = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCompletedCourses();
    }

    private async Task LoadCompletedCourses()
    {
        completedCourses = await DbContext.Courses
            .Where(c => c.Status == CourseStatus.Completed)
            .OrderByDescending(c => c.EndDate)
            .ToListAsync();
    }

    private async Task OnCourseChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int courseId) && courseId > 0)
        {
            selectedCourseId = courseId;
            selectedCourse = await DbContext.Courses.FindAsync(courseId);
            await LoadEligibleNominations();
        }
        else
        {
            selectedCourseId = 0;
            selectedCourse = null;
            eligibleNominations = null;
            selectedNominationIds.Clear();
        }
    }

    private async Task LoadEligibleNominations()
    {
        if (selectedCourseId == 0) return;

        // Get nominations that are completed but don't have certificates yet
        var existingCertificateEmployeeIds = await DbContext.Certificates
            .Where(c => c.CourseId == selectedCourseId)
            .Select(c => c.EmployeeId)
            .ToListAsync();

        eligibleNominations = await DbContext.CourseNominations
            .Include(n => n.Employee)
            .ThenInclude(e => e.Department)
            .Where(n => n.CourseId == selectedCourseId && 
                       n.Status == NominationStatus.Completed &&
                       !existingCertificateEmployeeIds.Contains(n.EmployeeId))
            .OrderBy(n => n.Employee.FirstName)
            .ThenBy(n => n.Employee.LastName)
            .ToListAsync();

        selectedNominationIds.Clear();
    }

    private void SelectAll()
    {
        if (eligibleNominations != null)
        {
            selectedNominationIds = eligibleNominations.Select(n => n.Id).ToHashSet();
        }
    }

    private void ClearAll()
    {
        selectedNominationIds.Clear();
    }

    private void ToggleAll(bool selectAll)
    {
        if (selectAll)
        {
            SelectAll();
        }
        else
        {
            ClearAll();
        }
    }

    private void ToggleNomination(int nominationId, bool isSelected)
    {
        if (isSelected)
        {
            selectedNominationIds.Add(nominationId);
        }
        else
        {
            selectedNominationIds.Remove(nominationId);
        }
    }

    private async Task GenerateCertificates()
    {
        if (selectedCourseId == 0 || selectedNominationIds.Count == 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار الدورة والمتدربين.");
            return;
        }

        isGenerating = true;
        try
        {
            var selectedNominations = eligibleNominations?
                .Where(n => selectedNominationIds.Contains(n.Id))
                .ToList();

            if (selectedNominations == null || !selectedNominations.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "لم يتم العثور على الترشيحات المحددة.");
                return;
            }

            var certificates = new List<Certificate>();
            var currentYear = DateTime.Now.Year;

            foreach (var nomination in selectedNominations)
            {
                // Generate certificate number
                var certificateCount = await DbContext.Certificates.CountAsync() + certificates.Count + 1;
                var certificateNumber = $"CERT-{currentYear}-{certificateCount:D6}";

                var certificate = new Certificate
                {
                    CertificateNumber = certificateNumber,
                    EmployeeId = nomination.EmployeeId,
                    CourseId = selectedCourseId,
                    IssueDate = DateTime.Today,
                    ExpiryDate = expiryDate,
                    IsActive = true
                };

                certificates.Add(certificate);
            }

            DbContext.Certificates.AddRange(certificates);
            await DbContext.SaveChangesAsync();

            await JSRuntime.InvokeVoidAsync("alert", $"تم إصدار {certificates.Count} شهادة بنجاح!");
            Navigation.NavigateTo("/certificates");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء إصدار الشهادات: {ex.Message}");
        }
        finally
        {
            isGenerating = false;
        }
    }
}
