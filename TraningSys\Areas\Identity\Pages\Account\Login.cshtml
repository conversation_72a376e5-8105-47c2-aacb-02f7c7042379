@page
@model LoginModel

@{
    ViewData["Title"] = "تسجيل الدخول";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h4>
            </div>
            <div class="card-body">
                <form id="account" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Input.Email" class="form-label">البريد الإلكتروني</label>
                        <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" />
                        <span asp-validation-for="Input.Email" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Input.Password" class="form-label">كلمة المرور</label>
                        <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" />
                        <span asp-validation-for="Input.Password" class="text-danger"></span>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" asp-for="Input.RememberMe" />
                        <label class="form-check-label" asp-for="Input.RememberMe">
                            تذكرني
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button id="login-submit" type="submit" class="btn btn-success">تسجيل الدخول</button>
                    </div>
                    
                    <div class="text-center mt-3">
                        <p>
                            <a id="register-link" asp-page="./Register">إنشاء حساب جديد</a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
