@page
@model TraningSys.Areas.Identity.Pages.Account.LoginModel

@{
    ViewData["Title"] = "تسجيل الدخول - نظام التدريب";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            min-height: 100vh;
        }
        .login-card {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            margin: 0 auto;
        }
        .login-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            padding: 2rem;
        }
        .login-body {
            padding: 2rem;
            background: white;
        }
        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .btn-login {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 16px;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }
        .text-danger {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="login-card">
            <div class="login-header text-white text-center">
                <i class="fas fa-graduation-cap fa-4x mb-3"></i>
                <h2 class="mb-1">نظام التدريب</h2>
                <p class="mb-0 opacity-75">مرحباً بك، يرجى تسجيل الدخول</p>
            </div>
            
            <div class="login-body">
                <form id="account" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3" role="alert"></div>
                    
                    <div class="mb-3">
                        <label asp-for="Input.UserName" class="form-label fw-bold">
                            <i class="fas fa-user me-2"></i>اسم المستخدم
                        </label>
                        <input asp-for="Input.UserName" class="form-control" autocomplete="username" 
                               aria-required="true" placeholder="أدخل اسم المستخدم" />
                        <span asp-validation-for="Input.UserName" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Input.Password" class="form-label fw-bold">
                            <i class="fas fa-lock me-2"></i>كلمة المرور
                        </label>
                        <input asp-for="Input.Password" class="form-control" autocomplete="current-password" 
                               aria-required="true" placeholder="أدخل كلمة المرور" />
                        <span asp-validation-for="Input.Password" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            <label class="form-check-label" asp-for="Input.RememberMe">
                                تذكرني
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button id="login-submit" type="submit" class="btn btn-success btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            للحصول على حساب، يرجى التواصل مع مدير النظام
                        </small>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <strong>حسابات تجريبية:</strong><br />
                        المدير: admin / Admin123!<br />
                        مستخدم: user1 / User123!
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/Identity/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
</body>
</html>
