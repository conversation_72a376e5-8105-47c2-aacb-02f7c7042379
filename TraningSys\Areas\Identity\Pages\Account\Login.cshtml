@page
@model TraningSys.Areas.Identity.Pages.Account.LoginModel

@{
    ViewData["Title"] = "تسجيل الدخول";
}

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);">
    <div class="row justify-content-center w-100">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-success text-white text-center py-4">
                    <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                    <h3 class="mb-0">نظام التدريب</h3>
                    <p class="mb-0">تسجيل الدخول</p>
                </div>
                <div class="card-body p-4">
                    <form id="account" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.UserName" class="form-label">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            <input asp-for="Input.UserName" class="form-control" autocomplete="username" 
                                   aria-required="true" placeholder="أدخل اسم المستخدم" />
                            <span asp-validation-for="Input.UserName" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Input.Password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            <input asp-for="Input.Password" type="password" class="form-control" autocomplete="current-password" 
                                   aria-required="true" placeholder="أدخل كلمة المرور" />
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" asp-for="Input.RememberMe" />
                                <label class="form-check-label" asp-for="Input.RememberMe">
                                    تذكرني
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button id="login-submit" type="submit" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <div class="alert alert-info">
                            <small>
                                <strong>حسابات تجريبية:</strong><br />
                                المدير: admin / Admin123!<br />
                                مستخدم: user1 / User123!
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
