@page "/certificates"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>إدارة الشهادات</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-certificate me-2"></i>إدارة الشهادات</h2>
    <div>
        <a href="/certificates/generate" class="btn btn-success me-2">
            <i class="fas fa-plus me-2"></i>إصدار شهادات جديدة
        </a>
        <a href="/certificates/create" class="btn btn-outline-success">
            <i class="fas fa-certificate me-2"></i>إضافة شهادة يدوياً
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-certificate fa-2x text-success mb-2"></i>
                <h4 class="mb-1">@totalCertificates</h4>
                <p class="mb-0 text-muted">إجمالي الشهادات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x text-primary mb-2"></i>
                <h4 class="mb-1">@thisMonthCertificates</h4>
                <p class="mb-0 text-muted">هذا الشهر</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x text-info mb-2"></i>
                <h4 class="mb-1">@internalCertificates</h4>
                <p class="mb-0 text-muted">دورات داخلية</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-university fa-2x text-warning mb-2"></i>
                <h4 class="mb-1">@externalCertificates</h4>
                <p class="mb-0 text-muted">دورات خارجية</p>
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h5 class="mb-0">قائمة الشهادات</h5>
            </div>
            <div class="col-md-4">
                <select class="form-select" @bind="selectedCourseType" @onchange="FilterByCourseType">
                    <option value="">جميع أنواع الدورات</option>
                    <option value="@CourseType.Internal">دورات داخلية</option>
                    <option value="@CourseType.External">دورات خارجية</option>
                </select>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في الشهادات..." 
                           @bind="searchTerm" @onkeyup="SearchCertificates" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="SearchCertificates">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        @if (certificates == null)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>
        }
        else if (!certificates.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-certificate fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد شهادات</h5>
                <p class="text-muted">ابدأ بإصدار شهادات جديدة</p>
                <a href="/certificates/generate" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إصدار شهادات
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الشهادة</th>
                            <th>الموظف</th>
                            <th>الدورة</th>
                            <th>نوع الدورة</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var certificate in certificates)
                        {
                            <tr>
                                <td>
                                    <div class="fw-bold text-primary">@certificate.CertificateNumber</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-success text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                            @certificate.Employee.FirstName.Substring(0, 1)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@certificate.Employee.FullName</div>
                                            <small class="text-muted">@certificate.Employee.EmployeeNumber</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">@certificate.Course.Name</div>
                                        <small class="text-muted">
                                            @certificate.Course.StartDate.ToString("dd/MM/yyyy") - @certificate.Course.EndDate.ToString("dd/MM/yyyy")
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    @if (certificate.Course.Type == CourseType.Internal)
                                    {
                                        <span class="badge bg-info">داخلية</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">خارجية</span>
                                    }
                                </td>
                                <td>@certificate.IssueDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    @if (certificate.ExpiryDate.HasValue)
                                    {
                                        <span class="@(certificate.ExpiryDate.Value < DateTime.Today ? "text-danger" : "")">
                                            @certificate.ExpiryDate.Value.ToString("dd/MM/yyyy")
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">لا تنتهي</span>
                                    }
                                </td>
                                <td>
                                    @if (certificate.IsActive)
                                    {
                                        @if (certificate.ExpiryDate.HasValue && certificate.ExpiryDate.Value < DateTime.Today)
                                        {
                                            <span class="badge bg-danger">منتهية الصلاحية</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">نشطة</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">ملغية</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/certificates/print/@certificate.Id" class="btn btn-sm btn-outline-primary" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <a href="/certificates/details/@certificate.Id" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/certificates/edit/@certificate.Id" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if (certificate.IsActive)
                                        {
                                            <button class="btn btn-sm btn-outline-danger" title="إلغاء الشهادة" 
                                                    @onclick="() => ConfirmRevoke(certificate.Id, certificate.CertificateNumber)">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-sm btn-outline-success" title="تفعيل الشهادة" 
                                                    @onclick="() => ActivateCertificate(certificate.Id)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض @certificates.Count() من أصل @totalCount شهادة
                    </small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage - 1)">السابق</button>
                        </li>
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => LoadPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage + 1)">التالي</button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    </div>
</div>

@code {
    private List<Certificate>? certificates;
    private string searchTerm = "";
    private string selectedCourseType = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages = 0;
    
    // Statistics
    private int totalCertificates = 0;
    private int thisMonthCertificates = 0;
    private int internalCertificates = 0;
    private int externalCertificates = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
        await LoadCertificates();
    }

    private async Task LoadStatistics()
    {
        totalCertificates = await DbContext.Certificates.CountAsync();
        
        var startOfMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
        thisMonthCertificates = await DbContext.Certificates
            .CountAsync(c => c.IssueDate >= startOfMonth);
        
        internalCertificates = await DbContext.Certificates
            .Include(c => c.Course)
            .CountAsync(c => c.Course.Type == CourseType.Internal);
        
        externalCertificates = await DbContext.Certificates
            .Include(c => c.Course)
            .CountAsync(c => c.Course.Type == CourseType.External);
    }

    private async Task LoadCertificates()
    {
        var query = DbContext.Certificates
            .Include(c => c.Employee)
            .Include(c => c.Course)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(c => 
                c.CertificateNumber.Contains(searchTerm) ||
                c.Employee.FirstName.Contains(searchTerm) ||
                c.Employee.LastName.Contains(searchTerm) ||
                c.Employee.EmployeeNumber.Contains(searchTerm) ||
                c.Course.Name.Contains(searchTerm));
        }

        if (!string.IsNullOrWhiteSpace(selectedCourseType) && Enum.TryParse<CourseType>(selectedCourseType, out var courseType))
        {
            query = query.Where(c => c.Course.Type == courseType);
        }

        totalCount = await query.CountAsync();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        certificates = await query
            .OrderByDescending(c => c.IssueDate)
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private async Task SearchCertificates()
    {
        currentPage = 1;
        await LoadCertificates();
    }

    private async Task FilterByCourseType()
    {
        currentPage = 1;
        await LoadCertificates();
    }

    private async Task LoadPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadCertificates();
        }
    }

    private async Task ConfirmRevoke(int certificateId, string certificateNumber)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"هل أنت متأكد من إلغاء الشهادة رقم '{certificateNumber}'؟\nهذا الإجراء سيجعل الشهادة غير نشطة.");
        
        if (confirmed)
        {
            await RevokeCertificate(certificateId);
        }
    }

    private async Task RevokeCertificate(int certificateId)
    {
        try
        {
            var certificate = await DbContext.Certificates.FindAsync(certificateId);
            if (certificate != null)
            {
                certificate.IsActive = false;
                await DbContext.SaveChangesAsync();
                await LoadStatistics();
                await LoadCertificates();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم إلغاء الشهادة بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء إلغاء الشهادة: {ex.Message}");
        }
    }

    private async Task ActivateCertificate(int certificateId)
    {
        try
        {
            var certificate = await DbContext.Certificates.FindAsync(certificateId);
            if (certificate != null)
            {
                certificate.IsActive = true;
                await DbContext.SaveChangesAsync();
                await LoadStatistics();
                await LoadCertificates();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم تفعيل الشهادة بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء تفعيل الشهادة: {ex.Message}");
        }
    }
}
