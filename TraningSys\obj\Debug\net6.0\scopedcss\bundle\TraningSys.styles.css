/* _content/TraningSys/Shared/MainLayout.razor.rz.scp.css */
body[b-zngkmlzzc2] {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

.sidebar[b-zngkmlzzc2] {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    min-height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    width: 250px;
    z-index: 1000;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.sidebar[b-zngkmlzzc2]  .nav-link {
    color: white !important;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 5px 10px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.sidebar[b-zngkmlzzc2]  .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

.sidebar[b-zngkmlzzc2]  .nav-link:hover {
    background-color: rgba(255,255,255,0.2);
    transform: translateX(-5px);
}

.sidebar[b-zngkmlzzc2]  .nav-link.active {
    background-color: rgba(255,255,255,0.3);
}

.main-content[b-zngkmlzzc2] {
    margin-right: 250px;
    padding: 0;
    min-height: 100vh;
}

.top-navbar[b-zngkmlzzc2] {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section[b-zngkmlzzc2] {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    margin-bottom: 20px;
}

.logo-section h4[b-zngkmlzzc2] {
    color: white;
    margin: 0;
    font-weight: 600;
}

.logo-section i[b-zngkmlzzc2] {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.content-wrapper[b-zngkmlzzc2] {
    padding: 20px 30px;
}

.user-info[b-zngkmlzzc2] {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar[b-zngkmlzzc2] {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

@@media (max-width: 768px) {
    .sidebar[b-zngkmlzzc2] {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show[b-zngkmlzzc2] {
        transform: translateX(0);
    }

    .main-content[b-zngkmlzzc2] {
        margin-right: 0;
    }

    .mobile-toggle[b-zngkmlzzc2] {
        display: block !important;
    }
}

.mobile-toggle[b-zngkmlzzc2] {
    display: none;
}
/* _content/TraningSys/Shared/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-kail55l7wg] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-kail55l7wg] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-kail55l7wg] {
    font-size: 1.1rem;
}

.oi[b-kail55l7wg] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-kail55l7wg] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-kail55l7wg] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-kail55l7wg] {
        padding-bottom: 1rem;
    }

    .nav-item[b-kail55l7wg]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-kail55l7wg]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-kail55l7wg]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-kail55l7wg] {
        display: none;
    }

    .collapse[b-kail55l7wg] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}
