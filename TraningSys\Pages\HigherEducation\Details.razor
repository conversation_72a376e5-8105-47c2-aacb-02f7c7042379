@page "/higher-education/details/{Id:int}"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation

<PageTitle>تفاصيل طلب الدراسات العليا</PageTitle>

@if (request == null)
{
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات الطلب...</p>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-graduate me-2"></i>تفاصيل طلب الدراسات العليا</h2>
        <div>
            <a href="/higher-education/edit/@Id" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>تعديل
            </a>
            <a href="/higher-education" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- Request Overview Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <div class="request-icon bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h4 class="mb-1">@request.Employee.FullName</h4>
                    <p class="text-muted mb-2">@request.Employee.EmployeeNumber</p>
                    
                    @switch (request.Status)
                    {
                        case RequestStatus.Submitted:
                            <span class="badge bg-primary fs-6">مقدم</span>
                            break;
                        case RequestStatus.UnderReview:
                            <span class="badge bg-warning fs-6">قيد المراجعة</span>
                            break;
                        case RequestStatus.Approved:
                            <span class="badge bg-success fs-6">موافق عليه</span>
                            break;
                        case RequestStatus.Rejected:
                            <span class="badge bg-danger fs-6">مرفوض</span>
                            break;
                        case RequestStatus.Completed:
                            <span class="badge bg-info fs-6">مكتمل</span>
                            break;
                    }
                </div>
            </div>

            <!-- Education Level Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>مستوى التعليم</h6>
                </div>
                <div class="card-body text-center">
                    @switch (request.EducationLevel)
                    {
                        case EducationLevel.Bachelor:
                            <div class="h3 text-primary mb-1">بكالوريوس</div>
                            <small class="text-muted">درجة البكالوريوس</small>
                            break;
                        case EducationLevel.HighDiploma:
                            <div class="h3 text-info mb-1">دبلوم عالي</div>
                            <small class="text-muted">دبلوم عالي</small>
                            break;
                        case EducationLevel.Master:
                            <div class="h3 text-success mb-1">ماجستير</div>
                            <small class="text-muted">درجة الماجستير</small>
                            break;
                        case EducationLevel.PhD:
                            <div class="h3 text-warning mb-1">دكتوراه</div>
                            <small class="text-muted">درجة الدكتوراه</small>
                            break;
                    }
                </div>
            </div>

            <!-- Cost Information -->
            @if (request.EstimatedCost.HasValue)
            {
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>التكلفة المقدرة</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="h3 text-success mb-1">@request.EstimatedCost.Value.ToString("N0") ريال</div>
                        <small class="text-muted">التكلفة الإجمالية المقدرة</small>
                    </div>
                </div>
            }
        </div>

        <div class="col-lg-8">
            <!-- Employee Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات الموظف</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">اسم الموظف</label>
                            <div class="fw-bold">@request.Employee.FullName</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">رقم الموظف</label>
                            <div class="fw-bold">@request.Employee.EmployeeNumber</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الإدارة</label>
                            <div>@request.Employee.Department?.Name</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">المنصب</label>
                            <div>@request.Employee.Position?.Name</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-university me-2"></i>المعلومات الأكاديمية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">التخصص</label>
                            <div class="fw-bold">@request.Specialization</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الجامعة</label>
                            <div class="fw-bold">@request.University</div>
                        </div>
                        @if (!string.IsNullOrEmpty(request.Country))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الدولة</label>
                                <div>
                                    <i class="fas fa-map-marker-alt me-1 text-danger"></i>@request.Country
                                </div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(request.ProgramDescription))
                        {
                            <div class="col-md-12 mb-3">
                                <label class="form-label text-muted">وصف البرنامج</label>
                                <div>@request.ProgramDescription</div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Timeline Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>الجدول الزمني</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ تقديم الطلب</label>
                            <div class="fw-bold">@request.RequestDate.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ البداية المتوقع</label>
                            <div class="fw-bold">@request.StartDate.ToString("dd/MM/yyyy")</div>
                        </div>
                        @if (request.EndDate.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ النهاية المتوقع</label>
                                <div class="fw-bold">@request.EndDate.Value.ToString("dd/MM/yyyy")</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">مدة الدراسة المقدرة</label>
                                <div class="fw-bold">@((request.EndDate.Value - request.StartDate).Days / 365.0:F1) سنة</div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(request.Notes))
                        {
                            <div class="col-md-12 mb-3">
                                <label class="form-label text-muted">ملاحظات</label>
                                <div class="alert alert-info">
                                    <i class="fas fa-sticky-note me-2"></i>@request.Notes
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Status History (Future Enhancement) -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>تاريخ الحالة</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم تقديم الطلب</h6>
                                <p class="timeline-description">@request.RequestDate.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                        </div>
                        @if (request.Status != RequestStatus.Submitted)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">
                                        @switch (request.Status)
                                        {
                                            case RequestStatus.UnderReview:
                                                <text>قيد المراجعة</text>
                                                break;
                                            case RequestStatus.Approved:
                                                <text>تمت الموافقة</text>
                                                break;
                                            case RequestStatus.Rejected:
                                                <text>تم الرفض</text>
                                                break;
                                            case RequestStatus.Completed:
                                                <text>تم الإكمال</text>
                                                break;
                                        }
                                    </h6>
                                    <p class="timeline-description">الحالة الحالية</p>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }

    .timeline-title {
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: 600;
    }

    .timeline-description {
        margin-bottom: 0;
        font-size: 12px;
        color: #6c757d;
    }
</style>

@code {
    [Parameter] public int Id { get; set; }
    
    private HigherEducationRequest? request;

    protected override async Task OnInitializedAsync()
    {
        await LoadRequest();
    }

    private async Task LoadRequest()
    {
        request = await DbContext.HigherEducationRequests
            .Include(r => r.Employee)
            .ThenInclude(e => e.Department)
            .Include(r => r.Employee)
            .ThenInclude(e => e.Position)
            .FirstOrDefaultAsync(r => r.Id == Id);

        if (request == null)
        {
            Navigation.NavigateTo("/higher-education");
        }
    }
}
