{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"TraningSys/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "6.0.25", "Microsoft.AspNetCore.Identity.UI": "6.0.25", "Microsoft.EntityFrameworkCore.Design": "6.0.25", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.25", "Microsoft.EntityFrameworkCore.Tools": "6.0.25"}, "runtime": {"TraningSys.dll": {}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "Microsoft.AspNetCore.Cryptography.Internal/6.0.25": {"runtime": {"lib/net6.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/6.0.25": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "6.0.25"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.25", "Microsoft.Extensions.Identity.Stores": "6.0.25"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.AspNetCore.Identity.UI/6.0.25": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "6.0.25", "Microsoft.Extensions.Identity.Stores": "6.0.25"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Identity.UI.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/2.1.4": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "4.7.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.25", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.25", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.25": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.25": {}, "Microsoft.EntityFrameworkCore.Design/6.0.25": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.25"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.25", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.25": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.4", "Microsoft.EntityFrameworkCore.Relational": "6.0.25"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "6.0.25.0", "fileVersion": "6.0.2523.52312"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.25": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.25"}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded/6.0.25": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.Extensions.Identity.Core/6.0.25": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "6.0.25", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.Extensions.Identity.Stores/6.0.25": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Identity.Core": "6.0.25", "Microsoft.Extensions.Logging": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.52315"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.8.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1523.11507"}}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding.CodePages/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}}}, "libraries": {"TraningSys/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-KKuDD1sD6cfYbdNQSZOaXqnic1mJ3FMTx5TOfhhmAJVWlwbmpwlmSTjmSvRGB6NVr5yx4kENmKcYr7JgEziogg==", "path": "microsoft.aspnetcore.cryptography.internal/6.0.25", "hashPath": "microsoft.aspnetcore.cryptography.internal.6.0.25.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-wbCg3/UaG334+i2ma9G0Gs478lzJV0+PDw7N100cSGLRG7KJSEmWzdIoLcgjHsmJ+xGAiQ+1h0zCz7VjGL3vRg==", "path": "microsoft.aspnetcore.cryptography.keyderivation/6.0.25", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.6.0.25.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-7oRXigpVioKyZ1EdlvbUlDQqlcLWuVojaOTrEwHAHkMSQNYOn47D87naV6oC/jqWreMO0RFI98LwcYrrgmIXcQ==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/6.0.25", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.6.0.25.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-UcFxaIYAbi7ihg/Zmja0H8etGOTReK5spBVhlbWQhKK3vkOxJWYoDL7lFSJ1tQKUQVl/WHTwKN++MG1DeWrZbw==", "path": "microsoft.aspnetcore.identity.ui/6.0.25", "hashPath": "microsoft.aspnetcore.identity.ui.6.0.25.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-cDcKBTKILdRuAzJjbgXwGcUQXzMue+SG02kD4tZTXXfoz4ALrGLpCnA5k9khw3fnAMlMnRzLIGuvRdJurqmESA==", "path": "microsoft.data.sqlclient/2.1.4", "hashPath": "microsoft.data.sqlclient.2.1.4.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-txcqw2xrmvMoTIgzAdUk8JHLELofGgTK3i6glswVZs4SC8BOU1M/iSAtwMIVtAtfzxuBIUAbHPx+Ly6lfkYe7g==", "path": "microsoft.entityframeworkcore/6.0.25", "hashPath": "microsoft.entityframeworkcore.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-DalO25C96LsIfAPlyizyun9y1XrIquRugPEGXC8+z7dFo+GyU0LRd0R11JDd3rJWjR18NOFYwqNenjyDpNRO3A==", "path": "microsoft.entityframeworkcore.abstractions/6.0.25", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-i6UpdWqWxSBbIFOkaMoubM40yIjTZO+0rIUkY5JRltSeFI4PzncBBQcNVNXXjAmiLXF/xY0xTS+ykClbkV46Yg==", "path": "microsoft.entityframeworkcore.analyzers/6.0.25", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-YawyMKj1f+GkwHrxMIf9tX84sMGgLFa5YoRmyuUugGhffiubkVLYIrlm4W0uSy2NzX4t6+V7keFLQf7lRQvDmA==", "path": "microsoft.entityframeworkcore.design/6.0.25", "hashPath": "microsoft.entityframeworkcore.design.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-ci2lR++x7R7LR71+HoeRnB9Z5VeOQ1ILLbFRhsjjWZyLrAMkdq7TK9Ll47jo1TXDWF8Ddeap1JgcptgPKkWSRA==", "path": "microsoft.entityframeworkcore.relational/6.0.25", "hashPath": "microsoft.entityframeworkcore.relational.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-/oIgY1cu75FiZc5gDfQQKgL9lv79Vearc+AY5WpZkoA0k+hJnkVYg68mdgbBZkJuyAC93cdKw8UXtCN0sUH7yg==", "path": "microsoft.entityframeworkcore.sqlserver/6.0.25", "hashPath": "microsoft.entityframeworkcore.sqlserver.6.0.25.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-2iPMR+DHXh2Xn9qoJ0ejzdHblpns73e1pZ/pyRbYDQi0HPJyq1/pTYDda1owJ5W2lxAGDg8l5Fl1jVp97fTR1g==", "path": "microsoft.entityframeworkcore.tools/6.0.25", "hashPath": "microsoft.entityframeworkcore.tools.6.0.25.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-q1S6iVA2+kvwuSt21zcjKLzQDGTyMwVlEGhucOu8xNIQiHjcvWrKHtwlBIJzIE1EIcxQL/4w3iQw9W665i5WdQ==", "path": "microsoft.extensions.fileproviders.embedded/6.0.25", "hashPath": "microsoft.extensions.fileproviders.embedded.6.0.25.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-01fkwJA2oMqjFDG6pz38vHEFlps94fuMOnbxFv9dmsKguW6rI5n7ki7s/nhLZhdyZ32stoR6GqkyvANw+PFprg==", "path": "microsoft.extensions.identity.core/6.0.25", "hashPath": "microsoft.extensions.identity.core.6.0.25.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/6.0.25": {"type": "package", "serviceable": true, "sha512": "sha512-mR1pKUAsuMaI9Yw5Zx2XfSvHWrN4MjCPOK9e9+KguZ9AmwpydacoVudFX78Pc1KFaW3RWO8JXD5yMasnG6SxnQ==", "path": "microsoft.extensions.identity.stores/6.0.25", "hashPath": "microsoft.extensions.identity.stores.6.0.25.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-aeu4FlaUTemuT1qOd1MyU4T516QR4Fy+9yDbwWMPHOHy7U8FD6SgTzdZFO7gHcfAPHtECqInbwklVvUK4RHcNg==", "path": "system.text.encoding.codepages/4.7.0", "hashPath": "system.text.encoding.codepages.4.7.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}}}