using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TraningSys.Models
{
    public enum CourseType
    {
        [Display(Name = "داخلية")]
        Internal = 1,
        [Display(Name = "خارجية")]
        External = 2
    }

    public enum CourseStatus
    {
        [Display(Name = "مجدولة")]
        Scheduled = 1,
        [Display(Name = "جارية")]
        InProgress = 2,
        [Display(Name = "مكتملة")]
        Completed = 3,
        [Display(Name = "ملغية")]
        Cancelled = 4
    }

    public class Course
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الدورة مطلوب")]
        [Display(Name = "اسم الدورة")]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(1000)]
        public string? Description { get; set; }

        [Required(ErrorMessage = "نوع الدورة مطلوب")]
        [Display(Name = "نوع الدورة")]
        public CourseType Type { get; set; }

        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        [Display(Name = "تاريخ البداية")]
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        [Display(Name = "تاريخ النهاية")]
        [DataType(DataType.Date)]
        public DateTime EndDate { get; set; }

        [Display(Name = "المدة بالأيام")]
        public int DurationDays => (EndDate - StartDate).Days + 1;

        [Display(Name = "المكان")]
        [StringLength(200)]
        public string? Location { get; set; }

        [Display(Name = "المدرب")]
        [StringLength(100)]
        public string? Instructor { get; set; }

        [Display(Name = "الحد الأقصى للمشاركين")]
        public int? MaxParticipants { get; set; }

        [Display(Name = "التكلفة")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }

        [Required(ErrorMessage = "حالة الدورة مطلوبة")]
        [Display(Name = "حالة الدورة")]
        public CourseStatus Status { get; set; } = CourseStatus.Scheduled;

        [Display(Name = "ملاحظات")]
        [StringLength(1000)]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<CourseNomination> CourseNominations { get; set; } = new List<CourseNomination>();
        public virtual ICollection<ExternalTrainee> ExternalTrainees { get; set; } = new List<ExternalTrainee>();
        public virtual ICollection<Certificate> Certificates { get; set; } = new List<Certificate>();
    }
}
