﻿@using Microsoft.AspNetCore.Components.Web
@namespace TraningSys.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />
    <title>@ViewData["Title"] - نظام التدريب</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <link href="css/site.css" rel="stylesheet" />
    <link href="TraningSys.styles.css" rel="stylesheet" />
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .sidebar {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            min-height: 100vh;
            position: fixed;
            right: 0;
            top: 0;
            width: 250px;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: white !important;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.3);
        }

        .main-content {
            margin-right: 250px;
            padding: 0;
        }

        .top-navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 30px;
            margin-bottom: 20px;
        }

        .logo-section {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .logo-section h4 {
            color: white;
            margin: 0;
            font-weight: 600;
        }

        .content-wrapper {
            padding: 20px 30px;
        }

        @@media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    @RenderBody()

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            حدث خطأ. قد لا يستجيب التطبيق حتى يتم إعادة تحميله.
        </environment>
        <environment include="Development">
            حدث استثناء غير معالج. راجع أدوات المطور في المتصفح للحصول على التفاصيل.
        </environment>
        <a href="" class="reload">إعادة تحميل</a>
        <a class="dismiss">🗙</a>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="_framework/blazor.server.js"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
