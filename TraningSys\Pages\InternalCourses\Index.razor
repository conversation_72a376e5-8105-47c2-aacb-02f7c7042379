@page "/internal-courses"
@using Microsoft.EntityFrameworkCore
@using TraningSys.Data
@using TraningSys.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>الدورات الداخلية</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chalkboard-teacher me-2"></i>الدورات الداخلية</h2>
    <a href="/internal-courses/create" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>إضافة دورة جديدة
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">قائمة الدورات الداخلية</h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في الدورات..." 
                           @bind="searchTerm" @onkeyup="SearchCourses" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="SearchCourses">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        @if (courses == null)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>
        }
        else if (!courses.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد دورات داخلية</h5>
                <p class="text-muted">ابدأ بإضافة دورة جديدة</p>
                <a href="/internal-courses/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دورة
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>اسم الدورة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>المدة</th>
                            <th>المدرب</th>
                            <th>المشاركين</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var course in courses)
                        {
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">@course.Name</div>
                                        @if (!string.IsNullOrEmpty(course.Location))
                                        {
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt me-1"></i>@course.Location
                                            </small>
                                        }
                                    </div>
                                </td>
                                <td>@course.StartDate.ToString("dd/MM/yyyy")</td>
                                <td>@course.EndDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    <span class="badge bg-info">@course.DurationDays يوم</span>
                                </td>
                                <td>@course.Instructor</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">@course.CourseNominations.Count()</span>
                                        @if (course.MaxParticipants.HasValue)
                                        {
                                            <span class="text-muted">/ @course.MaxParticipants</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    @switch (course.Status)
                                    {
                                        case CourseStatus.Scheduled:
                                            <span class="badge bg-primary">مجدولة</span>
                                            break;
                                        case CourseStatus.InProgress:
                                            <span class="badge bg-warning">جارية</span>
                                            break;
                                        case CourseStatus.Completed:
                                            <span class="badge bg-success">مكتملة</span>
                                            break;
                                        case CourseStatus.Cancelled:
                                            <span class="badge bg-danger">ملغية</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/internal-courses/details/@course.Id" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/internal-courses/edit/@course.Id" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/nominations/course/@course.Id" class="btn btn-sm btn-outline-success" title="إدارة الترشيحات">
                                            <i class="fas fa-users"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" title="حذف" 
                                                @onclick="() => ConfirmDelete(course.Id, course.Name)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض @courses.Count() من أصل @totalCount دورة
                    </small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage - 1)">السابق</button>
                        </li>
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => LoadPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => LoadPage(currentPage + 1)">التالي</button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    </div>
</div>

@code {
    private List<Course>? courses;
    private string searchTerm = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadCourses();
    }

    private async Task LoadCourses()
    {
        var query = DbContext.Courses
            .Include(c => c.CourseNominations)
            .Where(c => c.Type == CourseType.Internal)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(c => 
                c.Name.Contains(searchTerm) ||
                (c.Instructor != null && c.Instructor.Contains(searchTerm)) ||
                (c.Location != null && c.Location.Contains(searchTerm)));
        }

        totalCount = await query.CountAsync();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        courses = await query
            .OrderByDescending(c => c.StartDate)
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    private async Task SearchCourses()
    {
        currentPage = 1;
        await LoadCourses();
    }

    private async Task LoadPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadCourses();
        }
    }

    private async Task ConfirmDelete(int courseId, string courseName)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"هل أنت متأكد من حذف الدورة '{courseName}'؟\nهذا الإجراء لا يمكن التراجع عنه.");
        
        if (confirmed)
        {
            await DeleteCourse(courseId);
        }
    }

    private async Task DeleteCourse(int courseId)
    {
        try
        {
            var course = await DbContext.Courses.FindAsync(courseId);
            if (course != null)
            {
                DbContext.Courses.Remove(course);
                await DbContext.SaveChangesAsync();
                await LoadCourses();
                
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الدورة بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"حدث خطأ أثناء حذف الدورة: {ex.Message}");
        }
    }
}
