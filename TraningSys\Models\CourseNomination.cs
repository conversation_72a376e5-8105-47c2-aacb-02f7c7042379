using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TraningSys.Models
{
    public enum NominationStatus
    {
        [Display(Name = "مرشح")]
        Nominated = 1,
        [Display(Name = "مقبول")]
        Accepted = 2,
        [Display(Name = "مرفوض")]
        Rejected = 3,
        [Display(Name = "معتذر")]
        Excused = 4,
        [Display(Name = "مكتمل")]
        Completed = 5
    }

    public class CourseNomination
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "الموظف مطلوب")]
        [Display(Name = "الموظف")]
        public int EmployeeId { get; set; }

        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [Required(ErrorMessage = "الدورة مطلوبة")]
        [Display(Name = "الدورة")]
        public int CourseId { get; set; }

        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; } = null!;

        [Required(ErrorMessage = "حالة الترشيح مطلوبة")]
        [Display(Name = "حالة الترشيح")]
        public NominationStatus Status { get; set; } = NominationStatus.Nominated;

        [Display(Name = "تاريخ الترشيح")]
        public DateTime NominationDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ الرد")]
        public DateTime? ResponseDate { get; set; }

        [Display(Name = "سبب الرفض/الاعتذار")]
        [StringLength(500)]
        public string? RejectionReason { get; set; }

        [Display(Name = "ملاحظات")]
        [StringLength(1000)]
        public string? Notes { get; set; }

        [Display(Name = "إشعار مرسل")]
        public bool NotificationSent { get; set; } = false;

        [Display(Name = "تاريخ الإشعار")]
        public DateTime? NotificationDate { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }
    }
}
