@page
@model TraningSys.Areas.Identity.Pages.Account.RegisterModel
@{
    ViewData["Title"] = "غير مسموح";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row justify-content-center w-100">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-danger text-white text-center py-4">
                        <i class="fas fa-ban fa-3x mb-3"></i>
                        <h3 class="mb-0">غير مسموح</h3>
                        <p class="mb-0">إنشاء الحسابات معطل</p>
                    </div>
                    <div class="card-body p-4 text-center">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> إنشاء حسابات جديدة غير مسموح حالياً.
                            <br />
                            يرجى التواصل مع مدير النظام للحصول على حساب.
                        </div>

                        <a href="/Identity/Account/Login" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>العودة لتسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
