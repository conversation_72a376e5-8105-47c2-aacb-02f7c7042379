@page
@model TraningSys.Areas.Identity.Pages.Account.RegisterModel
@{
    ViewData["Title"] = "إنشاء حساب جديد";
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h4>
            </div>
            <div class="card-body">
                <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Input.FirstName" class="form-label">الاسم الأول</label>
                                <input asp-for="Input.FirstName" class="form-control" autocomplete="given-name" aria-required="true" />
                                <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Input.LastName" class="form-label">الاسم الأخير</label>
                                <input asp-for="Input.LastName" class="form-control" autocomplete="family-name" aria-required="true" />
                                <span asp-validation-for="Input.LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Input.UserName" class="form-label">اسم المستخدم</label>
                                <input asp-for="Input.UserName" class="form-control" autocomplete="username" aria-required="true" placeholder="اسم المستخدم" />
                                <span asp-validation-for="Input.UserName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Input.Email" class="form-label">البريد الإلكتروني</label>
                                <input asp-for="Input.Email" class="form-control" autocomplete="email" aria-required="true" placeholder="<EMAIL>" />
                                <span asp-validation-for="Input.Email" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Input.Password" class="form-label">كلمة المرور</label>
                                <input asp-for="Input.Password" class="form-control" autocomplete="new-password" aria-required="true" />
                                <span asp-validation-for="Input.Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Input.ConfirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" />
                                <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button id="registerSubmit" type="submit" class="btn btn-success">إنشاء الحساب</button>
                    </div>
                    
                    <div class="text-center mt-3">
                        <p>
                            <a asp-page="./Login">لديك حساب بالفعل؟ سجل الدخول</a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
